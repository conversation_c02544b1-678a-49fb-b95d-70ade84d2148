#============================================================================
# MÔ HÌNH HỒI QUY ĐA BIẾN CHO DỮ LIỆU TIỂU ĐƯỜNG
#============================================================================

#============================================================================
# 1. CHUẨN BỊ DỮ LIỆU
#============================================================================

# Cài đặt và tải các gói cần thiết
required_packages <- c("dplyr", "ggplot2", "ggcorrplot", "fastDummies", "car",
  "MASS", "caret", "glmnet", "psych", "factoextra")

# Cài đặt các gói chưa được cài đặt
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# Hàm tính các chỉ số đánh giá mô hình

calculate_metrics <- function(actual, predicted, family = "gaussian") {
  # Sai số truyền thống
  mse <- mean((actual - predicted)^2)
  rmse <- sqrt(mse)
  mae <- mean(abs(actual - predicted))
  r_squared <- 1 - sum((actual - predicted)^2) / sum((actual - mean(actual))^2)

  # Tính R2_deviance (chỉ dành cho GLM)
  if (family == "gamma") {
    eps <- .Machine$double.eps  # tránh log(0)
    # Deviance của mô hình
    dev_model <- 2 * sum((actual - predicted) / predicted - log(actual / predicted + eps))
    # Deviance của mô hình null
    mu_null <- mean(actual)
    dev_null <- 2 * sum((actual - mu_null) / mu_null - log(actual / mu_null + eps))
    r2_deviance <- 1 - dev_model / dev_null
  } else {
    r2_deviance <- NA
  }

  return(c(MSE = mse, RMSE = rmse, MAE = mae, R_squared = r_squared, R2_deviance = r2_deviance))
}


# Đọc dữ liệu từ file CSV
cat("Đọc dữ liệu...\n")
data <- read.csv("E:/20250504_themtuyen_tonghop.csv", colClasses = c(MA_CSKCB = "character"))

# Loại biến không sử dụng và Việt hóa tên biến
ma_the <- data$MA_THE
data <- data %>% dplyr::select('Ky_QT_adjust', 'Tuoi', 'GIOI_TINH', 'So_Ngay_DTri_adjust',
  'MA_LYDO_VVIEN', 'MA_LOAI_KCB', 'regions', 'HI', 'insurance',
  'tuyen', 'checkComorbidities', 'comorbidities', 
  'loaiBienChung', 'soBienChung', 'diemBienChung', "type", 'T_TONGCHI')

colnames(data) <- c('nam', 'tuoi', 'gioi_tinh', 'so_ngay_dtri',
  'ly_do_vvien', 'hinh_thuc_kcb', 'vung', 'doi_tuong', 'muc_huong',
  'tuyen', 'benh_kem', 'loai_benh_kem',
  'loai_bien_chung', 'so_bien_chung', 'diem_bien_chung', 'noi_dieu_tri', 'tong_chi_phi')

# colnames(data) <- c('Năm điều trị', 'Độ tuổi', 'Giới tính', 'Số ngày điều trị',
#   'Lý do vào viện', 'Hình thức khám chữa bệnh', 'Vùng - khu vực đăng ký BHYT',
#   'Nhóm đối tượng tham gia BHYT', 'Mức hưởng BHYT', 'Tuyến bệnh viện điều trị',
#   'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng', 'Số biến chứng',
#   'Điểm biến chứng', 'Nơi điều trị', 'Tổng chi phí')

# Tạo biến mới
# data$`Tổng chi phí` <- log(data$`Tổng chi phí`)
# data <- data %>% dplyr::select(-`Tổng chi phí`)
data$`nhom_tuoi` <- ifelse(data$`tuoi`<40, 1, ifelse(data$`tuoi`<60, 2, 3))
data$`so_bien_chung` <- ifelse(data$`so_bien_chung` == 0, '0',
  ifelse(data$`so_bien_chung` == 1, '1', ifelse(data$`so_bien_chung` == 2, '2', '≥3')))
data$`diem_bien_chung` <- ifelse(data$`diem_bien_chung` == 0, '0',
  ifelse(data$`diem_bien_chung` == 1, '1', ifelse(data$`diem_bien_chung` == 2, '2', '≥3')))

# Xác định biến phân loại và biến số
categorical_features = c('gioi_tinh', 'nhom_tuoi', 'ly_do_vvien', 'hinh_thuc_kcb',
  'vung', 'doi_tuong', 'muc_huong', 'tuyen', 'benh_kem', 'loai_benh_kem',
  'loai_bien_chung', 'so_bien_chung', 'diem_bien_chung', 'noi_dieu_tri')
numerical_features = c('nam', 'tuoi', 'so_ngay_dtri')
# categorical_features = c('Giới tính', 'Nhóm tuổi', 'Lý do vào viện', 'Hình thức khám chữa bệnh',
#   'Vùng - khu vực đăng ký BHYT', 'Nhóm đối tượng tham gia BHYT', 'Mức hưởng BHYT',
#   'Tuyến bệnh viện điều trị', 'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng',
#   'Số biến chứng', 'Điểm biến chứng', 'Nơi điều trị')


# Chuyển đổi biến phân loại thành factor
data[categorical_features] <- lapply(data[categorical_features], function(col) factor(col))
data$muc_huong <- factor(data$muc_huong, levels = c('80%', '95%', '100%'))
data$vung <- factor(data$vung,
  levels = c('Đồng Bằng Sông Hồng', 'Đông Nam Bộ', 'Bắc Trung Bộ và Duyên Hải Miền Trung',
  'Đồng Bằng Sông Cửu Long', 'Trung du và MN phía Bắc', 'Tây Nguyên', 'Bộ Quốc Phòng, Chính phủ'))
data$tuyen <- factor(data$tuyen, levels = c('Tỉnh và tương đương', 'Trung ương',
  'Quận, huyện và tương đương', 'Xã và tương đương', 'Y tế cơ quan'))
data$loai_bien_chung <- factor(data$loai_bien_chung, levels = c('Khong', 'BCCH', 'MML', 'MMN', 'MML+MMN',
  'MML+BCCH', 'MMN+BCCH', 'MML+MMN+BCCH'))
data$so_bien_chung <- factor(data$so_bien_chung, levels = c('0', '1', '2', '≥3'))
data$diem_bien_chung <- factor(data$diem_bien_chung, levels = c('0', '1', '2', '≥3'))
data$noi_dieu_tri <- factor(data$noi_dieu_tri, levels = c('HN', 'HCM'))


dim(data)
idx <- readRDS("idx_sai_so_lon.rds")
write.csv(idx, "idx_sai_so_lon.csv", row.names = FALSE)
data <- data %>% filter(so_ngay_dtri < 30 & !(row_number() %in% idx))
dim(data)
str(data)

# Mã hóa one-hot cho biến phân loại
cat("Mã hóa one-hot cho biến phân loại...\n")
onehot <- dummy_cols(data, select_columns = categorical_features,
  remove_first_dummy = TRUE, remove_selected_columns = TRUE)

rm(data)


#============================================================================
# 2. XỬ LÝ GIÁ TRỊ NGOẠI LAI (OUTLIERS)
#============================================================================

cat("Phát hiện và xử lý giá trị ngoại lai (outliers)...\n")

# Hàm phát hiện outliers sử dụng phương pháp IQR
detect_outliers_iqr <- function(x, multiplier = 1.5) {
  q1 <- quantile(x, 0.25, na.rm = TRUE)
  q3 <- quantile(x, 0.75, na.rm = TRUE)
  iqr <- q3 - q1
  lower_bound <- q1 - multiplier * iqr
  upper_bound <- q3 + multiplier * iqr
  return(x < lower_bound | x > upper_bound)
}

# Kiểm tra outliers cho biến phụ thuộc
outliers_to_remove <- detect_outliers_iqr(onehot$tong_chi_phi)
cat("Số lượng dòng dữ liệu bị loại bỏ do outliers:", sum(outliers_to_remove),
  "dòng (", round(sum(outliers_to_remove) / nrow(onehot) * 100, 2), "%)\n")

# Loại bỏ outliers
onehot <- onehot[!outliers_to_remove, ]
cat("Kích thước dữ liệu sau khi loại bỏ outliers:", nrow(onehot), "dòng\n")

# Giải phóng bộ nhớ
rm(outliers_to_remove)
gc()

#============================================================================
# 3. CHIA DỮ LIỆU THÀNH TẬP HUẤN LUYỆN VÀ TẬP KIỂM TRA
#============================================================================

cat("Chia dữ liệu thành tập huấn luyện và tập kiểm tra...\n")
set.seed(123)  # Đảm bảo tính tái lập
n <- nrow(onehot)
ind <- sample(1:n, size = 0.8 * n)

onehot_train_df <- onehot[ind, ]
onehot_test_df <- onehot[-ind, ]

cat("Kích thước tập huấn luyện:", nrow(onehot_train_df), "dòng\n")
cat("Kích thước tập kiểm tra:", nrow(onehot_test_df), "dòng\n")


corr_matrix <- cor(onehot_train_df[,c("so_bien_chung_1", "so_bien_chung_2", "so_bien_chung_≥3", "diem_bien_chung_1",
  "diem_bien_chung_2", "diem_bien_chung_≥3")])
corr_matrix <- round(corr_matrix, 2)
ord <- order(abs(corr_matrix["so_bien_chung_≥3", ]), decreasing = TRUE)
corr_matrix <- corr_matrix[ord, ord]
ggcorrplot(corr_matrix, type = "lower", lab = TRUE)

# # Chuẩn bị dữ liệu cho các mô hình
# x_train <- as.matrix(onehot_train_df[, !colnames(onehot_train_df) %in% "tong_chi_phi"])
# y_train <- onehot_train_df$`tong_chi_phi`
# x_test <- as.matrix(onehot_test_df[, !colnames(onehot_test_df) %in% "tong_chi_phi"])
# y_test <- onehot_test_df$`tong_chi_phi`

#============================================================================
# 4. XÂY DỰNG MÔ HÌNH
#============================================================================

#----------------------------------------------------------------------------
# 4.1 Phân tích thành phần chính (PCA)
#----------------------------------------------------------------------------

cat("\nPhương pháp 1: Principal Component Analysis (PCA)...\n")

# Thực hiện PCA
# Chuẩn bị dữ liệu cho PCA
# Loại bỏ biến phụ thuộc
pca_data <- onehot_train_df[, !colnames(onehot_train_df) %in% "tong_chi_phi"]

# Chuẩn hóa dữ liệu
pca_data_scaled <- scale(pca_data)

# Thực hiện PCA
pca_result <- prcomp(pca_data_scaled, center = TRUE, scale. = TRUE)

# Xác định số lượng thành phần chính để giữ lại 90% phương sai
pca_summary <- summary(pca_result)
cumulative_var <- cumsum(pca_summary$importance[2, ])
num_pcs <- which(cumulative_var >= 0.9)[1]
cat("Số lượng thành phần chính cần giữ lại để đạt 90% phương sai:", num_pcs, "\n")

# Trích xuất các thành phần chính và xây dựng mô hình
pc_scores <- as.data.frame(pca_result$x[, 1:num_pcs])
pc_train_data <- cbind(pc_scores, "tong_chi_phi" = onehot_train_df$`tong_chi_phi`)
pca_formula <- as.formula(paste("tong_chi_phi ~", paste(colnames(pc_scores), collapse = " + ")))
pca_model <- lm(pca_formula, data = pc_train_data)

# Tóm tắt mô hình PCA
summary_pca <- summary(pca_model)
cat("R-squared của mô hình PCA:", round(summary_pca$r.squared, 4), "\n")

# Dự đoán trên tập kiểm tra
pca_test_data <- predict(pca_result, newdata = onehot_test_df[, !colnames(onehot_test_df) %in% "tong_chi_phi"])
pca_test_data <- as.data.frame(pca_test_data[, 1:num_pcs])
predictions_pca <- predict(pca_model, newdata = pca_test_data)
metrics_pca <- calculate_metrics(y_test, predictions_pca)

#----------------------------------------------------------------------------
# 4.2 Lựa chọn biến với LASSO
#----------------------------------------------------------------------------

cat("\nPhương pháp 2: LASSO Regression...\n")

# Tìm tham số lambda tối ưu bằng cross-validation
set.seed(123)
cv_lasso <- cv.glmnet(x_train, y_train, alpha = 1, nfolds = 5)

# Xây dựng mô hình LASSO với lambda tối ưu
lasso_model <- glmnet(x_train, y_train, alpha = 1, lambda = cv_lasso$lambda.1se)

# Lấy các hệ số của mô hình LASSO
lasso_coef <- coef(lasso_model)
lasso_selected <- lasso_coef[which(lasso_coef != 0), ]
cat("Số biến được chọn bởi LASSO:", length(lasso_selected) - 1, "\n")  # Trừ 1 cho intercept

# Dự đoán trên tập kiểm tra
predictions_lasso <- predict(lasso_model, newx = x_test)
metrics_lasso <- calculate_metrics(y_test, predictions_lasso)

#----------------------------------------------------------------------------
# 4.3 Lựa chọn biến với Forward Stepwise
#----------------------------------------------------------------------------

cat("\nPhương pháp 3: Forward Stepwise Selection...\n")

# Tạo mô hình đầy đủ và mô hình rỗng
full_formula <- as.formula(paste("tong_chi_phi ~", paste(colnames(x_train), collapse = " + ")))
null_model <- lm(tong_chi_phi ~ 1, data = onehot_train_df)

# Thực hiện forward stepwise
forward_model <- stepAIC(null_model,
                         scope = list(lower = ~ 1, upper = full_formula),
                         direction = 'forward',
                         k = 2,
                         trace = 0)  # Tắt output chi tiết

summary_forward <- summary(forward_model)
cat("R-squared của mô hình Forward Stepwise:", round(summary_forward$r.squared, 4), "\n")
cat("Số biến trong mô hình Forward Stepwise:", length(coef(forward_model)) - 1, "\n")

# Dự đoán trên tập kiểm tra
predictions_forward <- predict(forward_model, newdata = onehot_test_df)
metrics_forward <- calculate_metrics(y_test, predictions_forward)

# Giải phóng bộ nhớ
rm(null_model)
gc()

#----------------------------------------------------------------------------
# 4.4 Mô hình GLM với family Gamma và link log
#----------------------------------------------------------------------------

cat("\nPhương pháp 4: GLM với family Gamma và link log...\n")

# Chuẩn bị dữ liệu cho GLM
# Lưu ý: GLM với Gamma yêu cầu biến phụ thuộc phải là dương

# # Vì chúng ta đang làm việc với Tổng chi phí, cần chuyển về Tổng chi phí
# onehot_train_df_glm <- onehot_train_df
# onehot_train_df_glm$`Tổng chi phí` <- exp(onehot_train_df_glm$`Tổng chi phí`)
# onehot_test_df_glm <- onehot_test_df
# onehot_test_df_glm$`Tổng chi phí` <- exp(onehot_test_df_glm$`Tổng chi phí`)

# # Sử dụng các biến quan trọng từ mô hình LASSO để xây dựng mô hình GLM
# Lấy tên các biến quan trọng từ LASSO (trừ intercept)
# important_vars <- names(lasso_selected)[-1]  # Loại bỏ intercept

# # Tạo công thức cho mô hình GLM
# if(length(important_vars) > 0) {
#   glm_formula <- as.formula(paste("`Tổng chi phí` ~", paste(important_vars, collapse = " + ")))
# } else {
#   # Nếu LASSO không chọn biến nào, sử dụng tất cả biến
#   glm_formula <- as.formula(paste("`Tổng chi phí` ~", paste(colnames(x_train), collapse = " + ")))
# }

# glm_formula <- as.formula("`Tổng chi phí` ~ `Năm điều trị` + `Độ tuổi` + `Số ngày điều trị` + `Giới tính_2` + `Nhóm tuổi_2` +
#   `Nhóm tuổi_3` + `Lý do vào viện_2` + `Lý do vào viện_3` + `Lý do vào viện_4` + `Hình thức khám chữa bệnh_2` +
#   `Hình thức khám chữa bệnh_3` + `Hình thức khám chữa bệnh_7` + `Hình thức khám chữa bệnh_9` +
#   `Vùng - khu vực đăng ký BHYT_Đông Nam Bộ` + `Vùng - khu vực đăng ký BHYT_Bắc Trung Bộ và Duyên Hải Miền Trung` +
#   `Vùng - khu vực đăng ký BHYT_Đồng Bằng Sông Cửu Long` + `Vùng - khu vực đăng ký BHYT_Trung du và MN phía Bắc` +
#   `Vùng - khu vực đăng ký BHYT_Tây Nguyên` + `Vùng - khu vực đăng ký BHYT_Bộ Quốc Phòng, Chính phủ` +
#   `Nhóm đối tượng tham gia BHYT_Ngân sách Nhà nước đóng` + `Nhóm đối tượng tham gia BHYT_Ngân sách Nhà nước hỗ trợ đóng` +
#   `Nhóm đối tượng tham gia BHYT_Người lao động, người sử dụng lao động đóng` + `Nhóm đối tượng tham gia BHYT_Tổ chức BHXH đóng` +
#   `Mức hưởng BHYT_95%` + `Mức hưởng BHYT_100%` + `Tuyến bệnh viện điều trị_Trung ương` + `Tuyến bệnh viện điều trị_Quận, huyện và tương đương` +
#   `Tuyến bệnh viện điều trị_Xã và tương đương` + `Tuyến bệnh viện điều trị_Y tế cơ quan` + `Bệnh kèm_1` +
#   `Loại bệnh kèm_E78` + `Loại bệnh kèm_E78+I10` + `Loại bệnh kèm_I10` + `Loại biến chứng_BCCH` + `Loại biến chứng_MML` +
#   `Loại biến chứng_MMN` + `Loại biến chứng_MML+MMN` + `Loại biến chứng_MML+BCCH` + `Loại biến chứng_MMN+BCCH` +
#   `Loại biến chứng_MML+MMN+BCCH` + `Số biến chứng_1` + `Số biến chứng_2` + `Số biến chứng_≥3` + `Điểm biến chứng_1` +
#   `Điểm biến chứng_2` + `Điểm biến chứng_≥3` + `Nơi điều trị_HCM`")

# Tạo công thức cho mô hình GLM
glm_formula <- as.formula(
  "tong_chi_phi ~ nam + tuoi + so_ngay_dtri + gioi_tinh_2 + nhom_tuoi_2 +
  nhom_tuoi_3 + ly_do_vvien_2 + ly_do_vvien_3 + ly_do_vvien_4 + hinh_thuc_kcb_2 +
  hinh_thuc_kcb_3 + hinh_thuc_kcb_7 + hinh_thuc_kcb_9 +
  `vung_Đông Nam Bộ` + `vung_Bắc Trung Bộ và Duyên Hải Miền Trung` +
  `vung_Đồng Bằng Sông Cửu Long` + `vung_Trung du và MN phía Bắc` +
  `vung_Tây Nguyên` + `vung_Bộ Quốc Phòng, Chính phủ` +
  `doi_tuong_Ngân sách Nhà nước đóng` + `doi_tuong_Ngân sách Nhà nước hỗ trợ đóng` +
  `doi_tuong_Người lao động, người sử dụng lao động đóng` + `doi_tuong_Tổ chức BHXH đóng` +
  `muc_huong_95%` + `muc_huong_100%` + `tuyen_Trung ương` + `tuyen_Quận, huyện và tương đương` +
  `tuyen_Xã và tương đương` + `tuyen_Y tế cơ quan` + benh_kem_1 +
  loai_benh_kem_E78 + `loai_benh_kem_E78+I10` + loai_benh_kem_I10 + loai_bien_chung_BCCH + loai_bien_chung_MML +
  loai_bien_chung_MMN + `loai_bien_chung_MML+MMN` + `loai_bien_chung_MML+BCCH` + `loai_bien_chung_MMN+BCCH` +
  `loai_bien_chung_MML+MMN+BCCH` + so_bien_chung_1 + so_bien_chung_2 + diem_bien_chung_1 +
  diem_bien_chung_2 + 
  noi_dieu_tri_HCM") # `so_bien_chung_≥3`, `diem_bien_chung_≥3`: loại do đa cộng tuyến hoàn hảo với các biến khác

# Xây dựng mô hình GLM với family Gamma và link log
glm_model <- glm(glm_formula,
                family = Gamma(link = "log"),
                data = onehot_train_df)
saveRDS(glm_model, file = "glm_model_3.rds")

# Tóm tắt mô hình GLM
summary_glm <- summary(glm_model)

library(broom)
glm_summary_df <- tidy(glm_model)
write.csv(glm_summary_df, "glm_summary.csv", row.names = FALSE)

alias(glm_model)$Complete
cat("AIC của mô hình GLM:", round(summary_glm$aic, 2), "\n")
cat("Số biến trong mô hình GLM:", length(coef(glm_model)) - 1, "\n")

table(onehot_train_df$`so_bien_chung_≥3`)


car::vif(glm_model)
# ll_model <- logLik(glm_model)
# ll_null <- logLik(update(glm_model, . ~ 1))
# r2_mcfadden <- 1 - (as.numeric(ll_model) / as.numeric(ll_null))
r2_deviance <- 1 - (deviance(glm_model) / glm_model$null.deviance)
n_obs <- nobs(glm_model)             # Số quan sát
p <- length(coef(glm_model)) - 1 # Số biến độc lập (trừ intercept)
r2_adj <- 1 - (1 - r2_deviance) * (n_obs - 1) / (n_obs - p - 1)
calculate_metrics(onehot_train_df$tong_chi_phi, predict(glm_model, type = "response"), family = "gamma")
min(onehot_train_df$tong_chi_phi)
max(onehot_train_df$tong_chi_phi)

onehot_train_df %>% filter(tong_chi_phi == 76)
# Dự đoán trên tập kiểm tra
predictions_glm <- predict(glm_model, newdata = onehot_test_df, type = "response")
# Chuyển đổi dự đoán và giá trị thực tế về log để so sánh với các mô hình khác
calculate_metrics(onehot_test_df$tong_chi_phi, predictions_glm, family = "gamma")
min(onehot_test_df$tong_chi_phi)
max(onehot_test_df$tong_chi_phi)
# Giải phóng bộ nhớ
# rm(onehot_train_df_glm, onehot_test_df_glm)
gc()

glm_model = readRDS("glm_model.rds")
max(onehot$tong_chi_phi)

predictions_org <- predict(glm_model, type = "response")
errors <- abs(predictions_org-onehot_train_df$tong_chi_phi)
relative_errors <- errors / onehot_train_df$tong_chi_phi
# idx <- which(errors > quantile(errors, 0.99))
# idx <- which(errors > 1e6)
# idx <- which(relative_errors > 0.3)
idx <- which(onehot_train_df$tong_chi_phi > 2e6 & relative_errors > 0.5 &
  onehot_train_df$so_ngay_dtri > 20)
x <- cbind(onehot_train_df[idx, c("so_ngay_dtri", "tong_chi_phi", "hinh_thuc_kcb_2", "hinh_thuc_kcb_3")],
           prediction = predictions_org[idx],
           error = errors[idx],
           relative_errors = relative_errors[idx])
dim(x)
View(x)
dim(x[x$hinh_thuc_kcb_3==1,])
dim(onehot_train_df[onehot_train_df$hinh_thuc_kcb_3==1,])
saveRDS(idx, file = "idx_sai_so_lon.rds")

# 3. Lấy chỉ số các dòng có so_ngay_dtri > 100
long_stay_idx <- which(onehot_train_df$so_ngay_dtri > 10)

# 4. Tìm bao nhiêu phần trăm long_stay_idx nằm trong idx
n_intersect <- sum(long_stay_idx %in% idx)
n_intersect / length(long_stay_idx) * 100



errors <- abs(predictions_glm-onehot_test_df$tong_chi_phi)
idx <- which(errors > quantile(errors, 0.99))
# 3. Lấy chỉ số các dòng có so_ngay_dtri > 100
long_stay_idx <- which(onehot_test_df$so_ngay_dtri > 30)

# 4. Tìm bao nhiêu phần trăm long_stay_idx nằm trong idx
n_intersect <- sum(long_stay_idx %in% idx)
n_intersect / length(long_stay_idx) * 100

dim(onehot[onehot$so_ngay_dtri>30,])

min(onehot[onehot$so_ngay_dtri>30,]$tong_chi_phi)

plot(onehot_test_df$tong_chi_phi, predictions_glm,
     main = "Actual vs Predicted",
     xlab = "Actual", ylab = "Predicted",
     pch = 19, col = rgb(0, 0, 1, 0.3))
abline(0, 1, col = "red", lwd = 2)
-

#============================================================================
# 5. SO SÁNH VÀ ĐÁNH GIÁ MÔ HÌNH
#============================================================================

cat("\nSo sánh các mô hình...\n")

# Tạo bảng so sánh các mô hình
metrics_df <- data.frame(
  Model = c("PCA", "LASSO", "Forward Stepwise", "GLM Gamma"),
  MSE = c(metrics_pca["MSE"], metrics_lasso["MSE"], metrics_forward["MSE"], metrics_glm["MSE"]),
  RMSE = c(metrics_pca["RMSE"], metrics_lasso["RMSE"], metrics_forward["RMSE"], metrics_glm["RMSE"]),
  MAE = c(metrics_pca["MAE"], metrics_lasso["MAE"], metrics_forward["MAE"], metrics_glm["MAE"]),
  R_squared = c(metrics_pca["R_squared"], metrics_lasso["R_squared"], metrics_forward["R_squared"], metrics_glm["R_squared"]),
  Variables = c(num_pcs, length(lasso_selected) - 1, length(coef(forward_model)) - 1, length(coef(glm_model)) - 1)
)

print(metrics_df)

# Xác định mô hình tốt nhất dựa trên R-squared
best_model_index <- which.max(metrics_df$R_squared)
best_model_name <- metrics_df$Model[best_model_index]
cat("\nMô hình tốt nhất dựa trên R-squared:", best_model_name, "\n")

# Lưu mô hình tốt nhất
best_model <- switch(best_model_name,
                    "PCA" = list(pca_result = pca_result, pca_model = pca_model),
                    "LASSO" = lasso_model,
                    "Forward Stepwise" = forward_model,
                    "GLM Gamma" = glm_model)

saveRDS(best_model, "best_diabetes_model.rds")
cat("Mô hình tốt nhất đã được lưu vào file 'best_diabetes_model.rds'\n")

#============================================================================
# 6. KẾT LUẬN
#============================================================================

cat("\n=== TÓM TẮT KẾT QUẢ ===\n")
cat("1. Kích thước dữ liệu:", n, "dòng\n")
cat("2. Mô hình tốt nhất:", best_model_name, "\n")
cat("3. R-squared trên tập kiểm tra:", round(metrics_df$R_squared[best_model_index], 4), "\n")
cat("4. RMSE trên tập kiểm tra:", round(metrics_df$RMSE[best_model_index], 4), "\n")
cat("5. Số biến trong mô hình:", metrics_df$Variables[best_model_index], "\n")

cat("\n=== GIẢI PHÁP XỬ LÝ DỮ LIỆU LỚN ===\n")
cat("1. Loại bỏ các giá trị ngoại lai (outliers) để cải thiện chất lượng mô hình\n")
cat("2. Sử dụng phân tích thành phần chính (PCA) để giảm số chiều dữ liệu\n")
cat("3. Sử dụng LASSO để lựa chọn biến hiệu quả\n")
cat("4. Sử dụng phương pháp Forward Stepwise thay vì Both Stepwise\n")
cat("5. Sử dụng mô hình GLM với family Gamma và link log cho dữ liệu chi phí\n")
cat("6. Tối ưu hóa bộ nhớ bằng cách giải phóng các đối tượng không cần thiết\n")
