#============================================================================
# MÔ HÌNH HỒI QUY ĐA BIẾN
#============================================================================

#============================================================================
# 1. CHUẨN BỊ DỮ LIỆU
#============================================================================

# Cài đặt và tải các gói cần thiết
if (!require(dplyr)) install.packages("dplyr")
if (!require(ggplot2)) install.packages("ggplot2")
if (!require(ggcorrplot)) install.packages("ggcorrplot")
if (!require(fastDummies)) install.packages("fastDummies")
if (!require(car)) install.packages("car")
if (!require(MASS)) install.packages("MASS")
if (!require(caret)) install.packages("caret")
if (!require(glmnet)) install.packages("glmnet")
if (!require(psych)) install.packages("psych")

library(dplyr)
library(ggplot2)
library(ggcorrplot)
library(fastDummies)
library(car)
library(MASS)
library(caret)
library(glmnet)
library(psych)

# Đọc dữ liệu từ file CSV
data <- read.csv("E:/20250504_themtuyen_tonghop.csv", colClasses = c(MA_CSKCB = "character"))

# Kiểm tra cấu trúc của dữ liệu
str(data)

# Tổng quan bộ dữ liệu
summary(data)

# Kiểm tra giá trị thiếu (NAs)
colSums(is.na(data))

# # Xử lý giá trị thiếu (nếu cần)
# # Ví dụ: thay thế NA bằng giá trị trung bình hoặc loại bỏ dòng có NA
# if(sum(na_counts) > 0) {
#   # Phương pháp 1: Loại bỏ dòng có NA
#   # data <- na.omit(data)

#   # Phương pháp 2: Thay thế NA bằng giá trị trung bình cho biến số
#   for(col in names(data)) {
#     if(is.numeric(data[[col]]) && sum(is.na(data[[col]])) > 0) {
#       data[[col]] <- ifelse(is.na(data[[col]]), mean(data[[col]], na.rm = TRUE), data[[col]])
#     }
#   }
# }

# Loại biến không sử dụng
data <- data %>% dplyr::select('Ky_QT_adjust', 'Tuoi', 'GIOI_TINH', 'So_Ngay_DTri_adjust',
  'MA_LYDO_VVIEN', 'MA_LOAI_KCB', 'regions', 'HI', 'insurance',
  'tuyen', 'checkComorbidities', 'comorbidities', 'loaiBienChung',
  'soBienChung', 'diemBienChung', "type", 'T_TONGCHI')

# Việt hóa tên biến
colnames(data) <- c('Năm điều trị', 'Độ tuổi', 'Giới tính', 'Số ngày điều trị',
  'Lý do vào viện', 'Hình thức khám chữa bệnh', 'Vùng - khu vực đăng ký BHYT', 'Nhóm đối tượng tham gia BHYT', 'Mức hưởng BHYT',
  'Tuyến bệnh viện điều trị', 'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng',
  'Số biến chứng', 'Điểm biến chứng', 'Nơi điều trị', 'Tổng chi phí')

# Tạo biến mới
data$`log(Tổng chi phí)` <- log(data$`Tổng chi phí`)
data <- data %>% dplyr::select(-`Tổng chi phí`)
data$`Nhóm tuổi` <- ifelse(data$`Độ tuổi`<40, 1,
  ifelse(data$`Độ tuổi`<60, 2, 3))
data$`Số biến chứng` <- ifelse(data$`Số biến chứng` == 0, '0',
  ifelse(data$`Số biến chứng` == 1, '1',
  ifelse(data$`Số biến chứng` == 2, '2', '≥3')))
data$`Điểm biến chứng` <- ifelse(data$`Điểm biến chứng` == 0, '0',
  ifelse(data$`Điểm biến chứng` == 1, '1',
  ifelse(data$`Điểm biến chứng` == 2, '2', '≥3')))

# Xác định biến phân loại và biến số
categorical_features = c('Giới tính', 'Nhóm tuổi', 'Lý do vào viện', 'Hình thức khám chữa bệnh',
  'Vùng - khu vực đăng ký BHYT', 'Nhóm đối tượng tham gia BHYT', 'Mức hưởng BHYT', 'Tuyến bệnh viện điều trị',
  'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng', 'Số biến chứng', 'Điểm biến chứng', 'Nơi điều trị')

numerical_features = c('Năm điều trị', 'Độ tuổi', 'Số ngày điều trị')

# Chuyển đổi biến phân loại thành factor và điều chỉnh nhóm tham chiếu
data[categorical_features] <- lapply(data[categorical_features], function(col) factor(col))

data$`Mức hưởng BHYT` <- factor(data$`Mức hưởng BHYT`, levels = c('80%', '95%', '100%'))

data$`Vùng - khu vực đăng ký BHYT` <- factor(data$`Vùng - khu vực đăng ký BHYT`,
  levels = c('Đồng Bằng Sông Hồng', 'Đông Nam Bộ', 'Bắc Trung Bộ và Duyên Hải Miền Trung',
  'Đồng Bằng Sông Cửu Long', 'Trung du và MN phía Bắc', 'Tây Nguyên', 'Bộ Quốc Phòng, Chính phủ'))

data$`Tuyến bệnh viện điều trị` <- factor(data$`Tuyến bệnh viện điều trị`, levels = c('Tỉnh và tương đương', 'Trung ương',
  'Quận, huyện và tương đương', 'Xã và tương đương', 'Y tế cơ quan'))

data$`Loại biến chứng` <- factor(data$`Loại biến chứng`, levels = c('Khong', 'BCCH', 'MML', 'MMN', 'MML+MMN',
  'MML+BCCH', 'MMN+BCCH', 'MML+MMN+BCCH'))

data$`Số biến chứng` <- factor(data$`Số biến chứng`, levels = c('0', '1', '2', '≥3'))

data$`Điểm biến chứng` <- factor(data$`Điểm biến chứng`, levels = c('0', '1', '2', '≥3'))

data$`Nơi điều trị` <- factor(data$`Nơi điều trị`, levels = c('HN', 'HCM'))

str(data)

# Kiểm tra giá trị ngoại lai (outliers) cho các biến số
# Sử dụng boxplot để trực quan hóa
par(mfrow = c(2, 2))
for(col in names(data)[sapply(data, is.numeric)][1:min(4, sum(sapply(data, is.numeric)))]) {
  boxplot(data[[col]], main = paste("Boxplot của", col))
}
par(mfrow = c(1, 1))

# ============================================================================
# 2. KHÁM PHÁ DỮ LIỆU (EDA - Exploratory Data Analysis)
# ============================================================================

# Thống kê mô tả chi tiết
describe(data)



# Kiểm tra phân phối của biến phụ thuộc
hist(data$`Tổng chi phí`, main = "Phân phối của Tổng chi phí", xlab = "Tổng chi phí", col = "lightblue")
hist(data$`log(Tổng chi phí)`, main = "Phân phối của log(Tổng chi phí)", xlab = "log(Tổng chi phí)", col = "lightblue")

# Kiểm tra phân phối chuẩn của biến phụ thuộc với Q-Q plot
qqnorm(data$`Tổng chi phí`, main = "Q-Q Plot của Tổng chi phí")
qqline(data$`Tổng chi phí`)

qqnorm(data$`log(Tổng chi phí)`, main = "Q-Q Plot của log(Tổng chi phí)")
qqline(data$`log(Tổng chi phí)`)

# ============================================================================
# 3. TIỀN XỬ LÝ DỮ LIỆU
# ============================================================================

# Biểu đồ phân tán giữa các biến số và biến phụ thuộc
par(mfrow = c(1, length(numerical_features)))
for(col in numerical_features) {
  plot(data[[col]], data[['log(Tổng chi phí)']],
       main = paste("Biểu đồ phân tán", col),
       xlab = col, ylab = "log(Tổng chi phí)")
  abline(lm(data[['log(Tổng chi phí)']] ~ data[[col]]), col = "red")
}
par(mfrow = c(1, 1))

# Biểu đồ boxplot cho biến phân loại và biến phụ thuộc
par(mfrow = c(2, 2))
for(col in categorical_features[1:min(4, length(categorical_features))]) {
  boxplot(data[['log(Tổng chi phí)']] ~ data[[col]],
          main = paste("Boxplot theo", col),
          xlab = col, ylab = "log(Tổng chi phí)")
}
par(mfrow = c(1, 1))

# Ma trận tương quan giữa các biến số
corr_matrix <- cor(data[c(numerical_features, "log(Tổng chi phí)")])
corr_matrix <- round(corr_matrix, 2)
ord <- order(abs(corr_matrix["log(Tổng chi phí)", ]), decreasing = TRUE)
corr_matrix <- corr_matrix[ord, ord]
ggcorrplot(corr_matrix, type = "lower", lab = TRUE)

# Mã hóa one-hot cho biến phân loại
onehot <- dummy_cols(data, select_columns = categorical_features,
                     remove_first_dummy = TRUE, remove_selected_columns = TRUE)
colnames(onehot)

# ============================================================================
# 3.1 XỬ LÝ GIÁ TRỊ NGOẠI LAI (OUTLIERS)
# ============================================================================

# Phát hiện outliers cho biến phụ thuộc và các biến số
cat("\nPhát hiện và xử lý giá trị ngoại lai (outliers)...\n")

# Hàm phát hiện outliers sử dụng phương pháp IQR (Interquartile Range)
detect_outliers_iqr <- function(x, multiplier = 1.5) {
  q1 <- quantile(x, 0.25, na.rm = TRUE)
  q3 <- quantile(x, 0.75, na.rm = TRUE)
  iqr <- q3 - q1
  lower_bound <- q1 - multiplier * iqr
  upper_bound <- q3 + multiplier * iqr

  return(list(
    outliers = x < lower_bound | x > upper_bound,
    lower_bound = lower_bound,
    upper_bound = upper_bound
  ))
}

# Hàm phát hiện outliers sử dụng phương pháp Z-score
detect_outliers_zscore <- function(x, threshold = 3) {
  z_scores <- scale(x)
  return(abs(z_scores) > threshold)
}

# Kiểm tra outliers cho biến phụ thuộc
log_cost <- onehot$`log(Tổng chi phí)`
outliers_log_cost_iqr <- detect_outliers_iqr(log_cost)
outliers_log_cost_zscore <- detect_outliers_zscore(log_cost)

# Hiển thị số lượng outliers theo từng phương pháp
cat("Số lượng outliers trong biến log(Tổng chi phí) theo phương pháp IQR:", sum(outliers_log_cost_iqr$outliers), "\n")
cat("Số lượng outliers trong biến log(Tổng chi phí) theo phương pháp Z-score:", sum(outliers_log_cost_zscore), "\n")

# Kiểm tra outliers cho các biến số
for (col in numerical_features) {
  if (col %in% colnames(onehot)) {
    outliers_iqr <- detect_outliers_iqr(onehot[[col]])
    outliers_zscore <- detect_outliers_zscore(onehot[[col]])

    cat("Số lượng outliers trong biến", col, "theo phương pháp IQR:", sum(outliers_iqr$outliers), "\n")
    cat("Số lượng outliers trong biến", col, "theo phương pháp Z-score:", sum(outliers_zscore), "\n")
  }
}

# Trực quan hóa outliers với boxplot
par(mfrow = c(2, 2))
for (col in c(numerical_features, "log(Tổng chi phí)")) {
  if (col %in% colnames(onehot)) {
    boxplot(onehot[[col]], main = paste("Boxplot của", col), col = "lightblue")
  }
}
par(mfrow = c(1, 1))

# Xử lý outliers - Chọn một trong các phương pháp sau:

# Phương pháp 1: Loại bỏ các outliers
# Sử dụng phương pháp IQR cho biến phụ thuộc
outliers_to_remove <- outliers_log_cost_iqr$outliers
cat("Số lượng dòng dữ liệu bị loại bỏ do outliers:", sum(outliers_to_remove), "dòng\n")
cat("Tỷ lệ dòng dữ liệu bị loại bỏ:", round(sum(outliers_to_remove) / nrow(onehot) * 100, 2), "%\n")

# Lưu kích thước dữ liệu trước khi loại bỏ outliers
original_size <- nrow(onehot)

# Loại bỏ outliers
onehot_no_outliers <- onehot[!outliers_to_remove, ]

# Hiển thị kích thước dữ liệu sau khi loại bỏ outliers
cat("Kích thước dữ liệu trước khi loại bỏ outliers:", original_size, "dòng\n")
cat("Kích thước dữ liệu sau khi loại bỏ outliers:", nrow(onehot_no_outliers), "dòng\n")

# Sử dụng dữ liệu đã loại bỏ outliers
onehot <- onehot_no_outliers

# Giải phóng bộ nhớ
rm(data, outliers_log_cost_iqr, outliers_log_cost_zscore, outliers_to_remove, onehot_no_outliers)
gc()

# ============================================================================
# 4. CHIA DỮ LIỆU VÀ XỬ LÝ DỮ LIỆU LỚN
# ============================================================================

# Kiểm tra kích thước dữ liệu
cat("Kích thước tổng thể của dữ liệu:", nrow(onehot), "dòng x", ncol(onehot), "cột\n")
cat("Bộ nhớ đã sử dụng (MB):", round(object.size(onehot)/1024^2, 2), "\n")

# Chia tập huấn luyện và tập kiểm tra từ mẫu đã lấy
set.seed(123)  # Đảm bảo tính tái lập
n <- nrow(onehot)
ind <- sample(1:n, size = 0.8 * n)

onehot_train_df <- onehot[ind, ]
onehot_test_df <- onehot[-ind, ]

# Kiểm tra kích thước của các tập
cat("Kích thước tập huấn luyện (sau khi lấy mẫu):", nrow(onehot_train_df), "dòng\n")
cat("Kích thước tập kiểm tra (sau khi lấy mẫu):", nrow(onehot_test_df), "dòng\n")

# Giải phóng bộ nhớ
gc()

# ============================================================================
# 5. XÂY DỰNG MÔ HÌNH
# ============================================================================

# 5.1 Kiểm tra đa cộng tuyến
# Chỉ kiểm tra VIF cho các biến số
cat("Kiểm tra đa cộng tuyến...\n")
vif_model <- lm(`log(Tổng chi phí)` ~ ., data = onehot_train_df[c(numerical_features, "log(Tổng chi phí)")])
vif_values <- car::vif(vif_model)
vif_values <- sort(vif_values, decreasing = TRUE)
print(vif_values)

# Hiển thị các biến có VIF > 5 (có thể điều chỉnh ngưỡng)
cat("Các biến có VIF > 5 (có thể có vấn đề đa cộng tuyến):\n")
print(vif_values[vif_values > 5])

# 5.2. Phân tích thành phần chính (PCA)
# Kiểm tra và cài đặt gói factoextra nếu cần
if (!require(factoextra)) install.packages("factoextra")
library(factoextra)

cat("\nPhương pháp 1: Principal Component Analysis (PCA)...\n")

# Chuẩn bị dữ liệu cho PCA
# Loại bỏ biến phụ thuộc
pca_data <- onehot_train_df[, !colnames(onehot_train_df) %in% "log(Tổng chi phí)"]

# Chuẩn hóa dữ liệu
pca_data_scaled <- scale(pca_data)

# Thực hiện PCA
pca_result <- prcomp(pca_data_scaled, center = TRUE, scale. = TRUE)

# Tóm tắt kết quả PCA
pca_summary <- summary(pca_result)
print(pca_summary)

# Vẽ biểu đồ scree plot để xác định số lượng thành phần chính cần giữ lại
fviz_eig(pca_result, addlabels = TRUE, ylim = c(0, 50))

# Xác định số lượng thành phần chính để giữ lại 80% phương sai
cumulative_var <- cumsum(pca_summary$importance[2, ])
num_pcs <- which(cumulative_var >= 0.9)[1]
cat("Số lượng thành phần chính cần giữ lại để đạt 80% phương sai:", num_pcs, "\n")

# Trích xuất các thành phần chính
pc_scores <- as.data.frame(pca_result$x[, 1:num_pcs])

# Thêm biến phụ thuộc vào dữ liệu PCA
pc_train_data <- cbind(pc_scores, "log(Tổng chi phí)" = onehot_train_df$`log(Tổng chi phí)`)

# Xây dựng mô hình hồi quy với các thành phần chính
pca_formula <- as.formula(paste("`log(Tổng chi phí)` ~", paste(colnames(pc_scores), collapse = " + ")))
pca_model <- lm(pca_formula, data = pc_train_data)

# Tóm tắt mô hình PCA
summary_pca <- summary(pca_model)
cat("R-squared của mô hình PCA:", round(summary_pca$r.squared, 4), "\n")
cat("Số thành phần chính trong mô hình PCA:", num_pcs, "\n")

# Plot
par(mfrow = c(2, 2))
plot(pca_model)
par(mfrow = c(1, 1))

# Loại bỏ các giá trị ngoại lai (outliers) để cải thiện chất lượng mô hình
library(gtsummary)
library(rio)
library(olsrr)
ols_plot_cooksd_bar(pca_model)
ols_plot_resid_lev(pca_model)
ols_plot_resid_stand(pca_model)
diag <- ols_plot_resid_lev(pca_model)
outliers <- pc_train_data[diag$data %>% filter(color=="outlier") %>% pull(obs),]
outlier_indices <- diag$data %>% filter(color == "outlier") %>% pull(obs)

pc_train_data_clean <- pc_train_data[-outlier_indices,]
pca_model_clean <- lm(pca_formula, data = pc_train_data_clean)
plot(pca_model_clean)

# Chuẩn bị dữ liệu kiểm tra cho PCA
pca_test_data <- predict(pca_result, newdata = onehot_test_df[, !colnames(onehot_test_df) %in% "log(Tổng chi phí)"])
pca_test_data <- as.data.frame(pca_test_data[, 1:num_pcs])

# Dự đoán trên tập kiểm tra
predictions_pca <- predict(pca_model, newdata = pca_test_data)
metrics_pca <- calculate_metrics(onehot_test_df$`log(Tổng chi phí)`, predictions_pca)
cat("RMSE của mô hình PCA trên tập kiểm tra:", round(metrics_pca["RMSE"], 4), "\n")
cat("R-squared của mô hình PCA trên tập kiểm tra:", round(metrics_pca["R_squared"], 4), "\n")

# Biểu đồ biplot để hiểu mối quan hệ giữa các biến và các thành phần chính
# Chỉ vẽ biplot với 2 thành phần chính đầu tiên và một số biến quan trọng nhất
if (ncol(pca_data) > 20) {
  # Nếu có quá nhiều biến, chỉ hiển thị top 20 biến có đóng góp lớn nhất
  var_contrib <- get_pca_var(pca_result)$contrib[, 1:2]
  top_vars_pca <- rownames(var_contrib)[order(rowSums(var_contrib), decreasing = TRUE)[1:20]]
  fviz_pca_var(pca_result, select.var = list(name = top_vars_pca))
} else {
  fviz_pca_var(pca_result)
}

# Lưu mô hình PCA để sử dụng sau
saveRDS(pca_result, "pca_model.rds")
saveRDS(pca_model, "pca_regression_model.rds")

# 5.3. Lựa chọn biến với LASSO
cat("\nPhương pháp 2: LASSO Regression...\n")

# Chuẩn bị dữ liệu cho LASSO
x_train <- as.matrix(onehot_train_df[, !colnames(onehot_train_df) %in% "log(Tổng chi phí)"])
y_train <- onehot_train_df$`log(Tổng chi phí)`
x_test <- as.matrix(onehot_test_df[, !colnames(onehot_test_df) %in% "log(Tổng chi phí)"])

# Tìm tham số lambda tối ưu bằng cross-validation
set.seed(123)
cv_lasso <- cv.glmnet(x_train, y_train, alpha = 1, nfolds = 3)
plot(cv_lasso)

# Hiển thị lambda tối ưu
cat("Lambda tối ưu (lambda.min):", cv_lasso$lambda.min, "\n")
cat("Lambda 1se (lambda.1se):", cv_lasso$lambda.1se, "\n")

# Xây dựng mô hình LASSO với lambda tối ưu
# Sử dụng lambda.1se để có mô hình đơn giản hơn nhưng vẫn có hiệu suất tốt
lasso_model <- glmnet(x_train, y_train, alpha = 1, lambda = cv_lasso$lambda.1se)

# Lấy các hệ số của mô hình LASSO
lasso_coef <- coef(lasso_model)
lasso_selected <- lasso_coef[which(lasso_coef != 0), ]
cat("Số biến được chọn bởi LASSO:", length(lasso_selected) - 1, "\n")  # Trừ 1 cho intercept

# Hiển thị các biến quan trọng nhất theo LASSO
lasso_sorted <- lasso_selected[order(abs(lasso_selected), decreasing = TRUE)]
cat("\nTop biến quan trọng nhất theo LASSO:\n")
print(head(lasso_sorted, 10))

# Dự đoán trên tập kiểm tra
predictions_lasso <- predict(lasso_model, newx = x_test)
metrics_lasso <- calculate_metrics(onehot_test_df$`log(Tổng chi phí)`, predictions_lasso)
cat("RMSE của mô hình LASSO trên tập kiểm tra:", round(metrics_lasso["RMSE"], 4), "\n")
cat("R-squared của mô hình LASSO trên tập kiểm tra:", round(metrics_lasso["R_squared"], 4), "\n")

# Lưu mô hình LASSO để sử dụng sau
saveRDS(lasso_model, "lasso_model.rds")
saveRDS(lasso_selected, "lasso_selected.rds")

# 5.4.Lựa chọn biến với Forward Stepwise
# Sử dụng phương pháp forward
cat("\nPhương pháp 3: Forward Stepwise Selection...\n")

# Tạo mô hình đầy đủ (với tất cả biến dự kiến đưa vào)
full_formula <- as.formula(
  "`log(Tổng chi phí)` ~ `Năm điều trị` + `Độ tuổi` + `Số ngày điều trị` + `Giới tính_2` + `Nhóm tuổi_2` +
  `Nhóm tuổi_3` + `Lý do vào viện_2` + `Lý do vào viện_3` + `Lý do vào viện_4` + `Hình thức khám chữa bệnh_2` +
  `Hình thức khám chữa bệnh_3` + `Hình thức khám chữa bệnh_7` + `Hình thức khám chữa bệnh_9` +
  `Vùng - khu vực đăng ký BHYT_Đông Nam Bộ` + `Vùng - khu vực đăng ký BHYT_Bắc Trung Bộ và Duyên Hải Miền Trung` +
  `Vùng - khu vực đăng ký BHYT_Đồng Bằng Sông Cửu Long` + `Vùng - khu vực đăng ký BHYT_Trung du và MN phía Bắc` +
  `Vùng - khu vực đăng ký BHYT_Tây Nguyên` + `Vùng - khu vực đăng ký BHYT_Bộ Quốc Phòng, Chính phủ` +
  `Nhóm đối tượng tham gia BHYT_Ngân sách Nhà nước đóng` + `Nhóm đối tượng tham gia BHYT_Ngân sách Nhà nước hỗ trợ đóng` +
  `Nhóm đối tượng tham gia BHYT_Người lao động, người sử dụng lao động đóng` + `Nhóm đối tượng tham gia BHYT_Tổ chức BHXH đóng` +
  `Mức hưởng BHYT_95%` + `Mức hưởng BHYT_100%` + `Tuyến bệnh viện điều trị_Trung ương` + `Tuyến bệnh viện điều trị_Quận, huyện và tương đương` +
  `Tuyến bệnh viện điều trị_Xã và tương đương` + `Tuyến bệnh viện điều trị_Y tế cơ quan` + `Bệnh kèm_1` +
  `Loại bệnh kèm_E78` + `Loại bệnh kèm_E78+I10` + `Loại bệnh kèm_I10` + `Loại biến chứng_BCCH` + `Loại biến chứng_MML` +
  `Loại biến chứng_MMN` + `Loại biến chứng_MML+MMN` + `Loại biến chứng_MML+BCCH` + `Loại biến chứng_MMN+BCCH` +
  `Loại biến chứng_MML+MMN+BCCH` + `Số biến chứng_1` + `Số biến chứng_2` + `Số biến chứng_≥3` + `Điểm biến chứng_1` +
  `Điểm biến chứng_2` + `Điểm biến chứng_≥3` + `Nơi điều trị_HCM`"
)

# Mô hình rỗng (null model) chỉ có intercept
null_model <- lm(`log(Tổng chi phí)` ~ 1, data = onehot_train_df)
# add1(null_model, scope = full_formula, test = "F")

# Thực hiện forward stepwise
forward_model <- stepAIC(null_model,
                         scope = list(lower = ~ 1, upper = full_formula),
                         direction = 'forward',
                         k = 2,
                         trace = 1)

summary_forward <- summary(forward_model)
cat("R-squared của mô hình Forward Stepwise:", round(summary_forward$r.squared, 4), "\n")
cat("Số biến trong mô hình Forward Stepwise:", length(coef(forward_model)) - 1, "\n")


# Giải phóng bộ nhớ
rm(null_model)
gc()

# ============================================================================
# 6. KIỂM TRA GIẢ ĐỊNH CỦA MÔ HÌNH
# ============================================================================

# Tạo biểu đồ chẩn đoán cho mô hình stepwise
par(mfrow = c(2, 2))
plot(forward_model)
par(mfrow = c(1, 1))

# Kiểm tra điểm ảnh hưởng (influential points)
influence_plot <- influencePlot(forward_model, id.method = "identify",
                               main = "Biểu đồ ảnh hưởng",
                               sub = "Kích thước điểm tỷ lệ với Cook's distance")

# Kiểm tra phần dư chuẩn hóa
residuals_data <- data.frame(
  Fitted = fitted(forward_model),
  Residuals = rstandard(forward_model)
)

ggplot(residuals_data, aes(x = Fitted, y = Residuals)) +
  geom_point() +
  geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
  geom_smooth(method = "loess", se = FALSE, color = "blue") +
  labs(title = "Biểu đồ phần dư chuẩn hóa",
       x = "Giá trị dự đoán",
       y = "Phần dư chuẩn hóa") +
  theme_minimal()

# ============================================================================
# 7. ĐÁNH GIÁ MÔ HÌNH
# ============================================================================

# Dự đoán trên tập kiểm tra
cat("\nĐánh giá các mô hình trên tập kiểm tra...\n")
y_test <- onehot_test_df[["log(Tổng chi phí)"]]

# Tính các chỉ số đánh giá
calculate_metrics <- function(actual, predicted) {
  mse <- mean((actual - predicted)^2)
  rmse <- sqrt(mse)
  mae <- mean(abs(actual - predicted))
  r_squared <- 1 - sum((actual - predicted)^2) / sum((actual - mean(actual))^2)

  return(c(MSE = mse, RMSE = rmse, MAE = mae, R_squared = r_squared))
}

# Dự đoán với các mô hình
# Mô hình PCA đã được dự đoán trước đó trong phần 5.2
# predictions_pca và metrics_pca đã được tính toán

# Dự đoán với mô hình Forward Stepwise
predictions_forward <- predict(forward_model, newdata = onehot_test_df)
metrics_forward <- calculate_metrics(y_test, predictions_forward)

# Tạo bảng so sánh các mô hình
metrics_df <- data.frame(
  Model = c("PCA", "LASSO", "Forward Stepwise"),
  MSE = c(metrics_pca["MSE"], metrics_lasso["MSE"], metrics_forward["MSE"]),
  RMSE = c(metrics_pca["RMSE"], metrics_lasso["RMSE"], metrics_forward["RMSE"]),
  MAE = c(metrics_pca["MAE"], metrics_lasso["MAE"], metrics_forward["MAE"]),
  R_squared = c(metrics_pca["R_squared"], metrics_lasso["R_squared"], metrics_forward["R_squared"]),
  Variables = c(num_pcs, length(lasso_selected) - 1, length(coef(forward_model)) - 1)
)

# Kiểm tra xem các mô hình khác có tồn tại không trước khi thêm vào bảng so sánh
if(exists("full_model")) {
  predictions_full <- predict(full_model, newdata = onehot_test_df)
  metrics_full <- calculate_metrics(y_test, predictions_full)
  metrics_df <- rbind(metrics_df,
    data.frame(Model = "Full Model",
                               MSE = metrics_full["MSE"],
                               RMSE = metrics_full["RMSE"],
                               MAE = metrics_full["MAE"],
                               R_squared = metrics_full["R_squared"],
                               Variables = length(coef(full_model)) - 1))
}

if(exists("correlation_model")) {
  predictions_cor <- predict(correlation_model, newdata = onehot_test_df)
  metrics_cor <- calculate_metrics(y_test, predictions_cor)
  metrics_df <- rbind(metrics_df,
                     data.frame(Model = "Correlation-based",
                               MSE = metrics_cor["MSE"],
                               RMSE = metrics_cor["RMSE"],
                               MAE = metrics_cor["MAE"],
                               R_squared = metrics_cor["R_squared"],
                               Variables = length(coef(correlation_model)) - 1))
}

print(metrics_df)

# Xác định mô hình tốt nhất dựa trên R-squared
best_model_index <- which.max(metrics_df$R_squared)
best_model_name <- metrics_df$Model[best_model_index]
cat("\nMô hình tốt nhất dựa trên R-squared:", best_model_name, "\n")

# Trực quan hóa kết quả dự đoán vs thực tế cho mô hình tốt nhất
best_predictions <- switch(best_model_name,
                          "PCA" = predictions_pca,
                          "LASSO" = predictions_lasso,
                          "Forward Stepwise" = predictions_forward)

prediction_data <- data.frame(
  Actual = y_test,
  Predicted = best_predictions
)

ggplot(prediction_data, aes(x = Actual, y = Predicted)) +
  geom_point(alpha = 0.5) +
  geom_abline(intercept = 0, slope = 1, color = "red") +
  labs(title = paste("Giá trị thực tế vs. Dự đoán (", best_model_name, ")", sep = ""),
       x = "log(Tổng chi phí) thực tế",
       y = "log(Tổng chi phí) dự đoán") +
  theme_minimal()

# ============================================================================
# 8. DIỄN GIẢI KẾT QUẢ
# ============================================================================

# Hiển thị hệ số hồi quy và ý nghĩa thống kê của mô hình tốt nhất
cat("\nDiễn giải kết quả của mô hình", best_model_name, "...\n")

# Lưu mô hình tốt nhất
best_model <- switch(best_model_name,
                    "PCA" = list(pca_result = pca_result, pca_model = pca_model),
                    "Forward Stepwise" = forward_model,
                    "Full Model" = if(exists("full_model")) full_model else NULL,
                    "LASSO" = if(exists("lasso_model")) lasso_model else NULL,
                    "Correlation-based" = if(exists("correlation_model")) correlation_model else NULL)

# Xử lý theo loại mô hình
if (best_model_name %in% c("Full Model", "Forward Stepwise", "Correlation-based")) {
  # Mô hình hồi quy tuyến tính
  model_summary <- summary(best_model)
  coef_table <- model_summary$coefficients

  # Sắp xếp hệ số theo giá trị tuyệt đối giảm dần
  coef_sorted <- coef_table[order(abs(coef_table[, "Estimate"]), decreasing = TRUE), ]
  cat("\nTop 10 biến quan trọng nhất (theo giá trị tuyệt đối của hệ số):\n")
  print(head(coef_sorted, 10))

  # Hiển thị các biến có ý nghĩa thống kê (p < 0.05)
  significant_vars <- coef_table[coef_table[, "Pr(>|t|)"] < 0.05, ]
  cat("\nSố biến có ý nghĩa thống kê (p < 0.05):", nrow(significant_vars), "\n")

  # Lưu các biến quan trọng nhất
  top_vars <- rownames(coef_sorted)[2:min(11, nrow(coef_sorted))]

} else if (best_model_name == "LASSO") {
  # Mô hình LASSO
  cat("\nCác biến được chọn bởi LASSO (hệ số khác 0):\n")
  lasso_sorted <- lasso_selected[order(abs(lasso_selected), decreasing = TRUE)]
  print(lasso_sorted)

  # Lưu các biến quan trọng nhất
  top_vars <- names(lasso_sorted)[2:min(11, length(lasso_sorted))]

} else if (best_model_name == "PCA") {
  # Mô hình PCA
  model_summary <- summary(best_model$pca_model)
  cat("\nThông tin về các thành phần chính được sử dụng:\n")
  print(summary(best_model$pca_result)$importance[, 1:min(5, num_pcs)])

  # Hiển thị các thành phần chính có ý nghĩa thống kê
  coef_table <- model_summary$coefficients
  significant_pcs <- coef_table[coef_table[, "Pr(>|t|)"] < 0.05, ]
  cat("\nSố thành phần chính có ý nghĩa thống kê (p < 0.05):", nrow(significant_pcs) - 1, "\n")

  # Lưu các thành phần chính quan trọng nhất
  coef_sorted <- coef_table[order(abs(coef_table[, "Estimate"]), decreasing = TRUE), ]
  top_vars <- rownames(coef_sorted)[2:min(11, nrow(coef_sorted))]
}

# ============================================================================
# 9. KIỂM CHỨNG CHÉO (CROSS-VALIDATION) - TỐI ƯU HÓA CHO DỮ LIỆU LỚN
# ============================================================================

# Sử dụng mẫu nhỏ hơn cho cross-validation để tránh tràn bộ nhớ
cat("\nThực hiện cross-validation trên mẫu nhỏ...\n")
set.seed(123)
cv_sample_size <- min(10000, nrow(onehot))
cv_indices <- sample(1:nrow(onehot), size = cv_sample_size)
cv_data <- onehot[cv_indices, ]

# Thiết lập k-fold cross-validation với số fold nhỏ hơn
train_control <- trainControl(method = "cv", number = 5)  # Giảm số fold xuống 5

# Thực hiện cross-validation với mô hình đơn giản hơn
# Sử dụng các biến quan trọng nhất thay vì tất cả các biến
if (exists("top_vars")) {
  formula_cv <- as.formula(paste("`log(Tổng chi phí)` ~", paste(top_vars, collapse = " + ")))

  cv_model <- train(
    formula_cv,
    data = cv_data,
    method = "lm",
    trControl = train_control
  )

  # Hiển thị kết quả cross-validation
  cat("\nKết quả cross-validation (5-fold):\n")
  print(cv_model$results)
  cat("R-squared trung bình qua 5 fold:", round(cv_model$results$Rsquared, 4), "\n")
}

# ============================================================================
# 10. BÁO CÁO VÀ TRÌNH BÀY
# ============================================================================

# Tóm tắt kết quả chính
cat("\n=== TÓM TẮT KẾT QUẢ ===\n")
cat("1. Kích thước dữ liệu gốc:", n, "dòng\n")
cat("2. Kích thước mẫu sử dụng:", n, "dòng\n")
cat("3. Mô hình tốt nhất:", best_model_name, "\n")
cat("4. R-squared trên tập kiểm tra:", round(metrics_df$R_squared[best_model_index], 4), "\n")
cat("5. RMSE trên tập kiểm tra:", round(metrics_df$RMSE[best_model_index], 4), "\n")
cat("6. Số biến trong mô hình:", metrics_df$Variables[best_model_index], "\n")

if (exists("top_vars")) {
  cat("7. Các biến quan trọng nhất:\n   ", paste(top_vars, collapse = "\n    "), "\n")
}

# Lưu mô hình tốt nhất
saveRDS(best_model, "best_diabetes_model.rds")
cat("Mô hình tốt nhất đã được lưu vào file 'best_diabetes_model.rds'\n")

# Kết luận
cat("\n=== KẾT LUẬN ===\n")
cat("Mô hình hồi quy đa biến đã được xây dựng thành công để dự đoán log(Tổng chi phí) điều trị bệnh tiểu đường.\n")

if (exists("model_summary")) {
  cat("Mô hình có khả năng giải thích", round(model_summary$r.squared * 100, 2), "% biến thiên của log(Tổng chi phí).\n")
} else {
  cat("Mô hình có khả năng giải thích", round(metrics_df$R_squared[best_model_index] * 100, 2), "% biến thiên của log(Tổng chi phí).\n")
}

cat("\n=== GIẢI PHÁP XỬ LÝ DỮ LIỆU LỚN ===\n")
cat("1. Lấy mẫu ngẫu nhiên từ tập dữ liệu gốc\n")
cat("2. Loại bỏ các giá trị ngoại lai (outliers) để cải thiện chất lượng mô hình\n")
cat("3. Sử dụng phương pháp Forward Stepwise thay vì Both Stepwise\n")
cat("4. Sử dụng LASSO để lựa chọn biến hiệu quả\n")
cat("5. Sử dụng phương pháp lựa chọn biến dựa trên tương quan\n")
cat("6. Sử dụng phân tích thành phần chính (PCA) để giảm số chiều dữ liệu\n")
cat("7. Tối ưu hóa bộ nhớ bằng cách giải phóng các đối tượng không cần thiết\n")
cat("8. Giảm số fold trong cross-validation\n")

# Ghi chú về việc áp dụng mô hình cho toàn bộ dữ liệu
cat("\n=== GHI CHÚ ===\n")
cat("Để áp dụng mô hình cho toàn bộ dữ liệu, bạn có thể:\n")
cat("1. Sử dụng các biến đã được chọn từ mô hình tốt nhất để xây dựng mô hình trên toàn bộ dữ liệu\n")
cat("2. Sử dụng các gói R đặc biệt cho dữ liệu lớn như 'biglm', 'bigmemory', hoặc 'ff'\n")
cat("3. Xử lý dữ liệu theo từng phần (chunking) nếu cần thiết\n")

# Giải phóng bộ nhớ cuối cùng
rm(list = setdiff(ls(), c("best_model", "metrics_df", "best_model_name")))
gc()