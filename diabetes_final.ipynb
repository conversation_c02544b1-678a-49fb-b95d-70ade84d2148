# Import c<PERSON><PERSON> thư viện cần thiết
# <PERSON><PERSON><PERSON> viện xử lý dữ liệu
import pandas as pd
import numpy as np

# Th<PERSON> viện trực quan hóa dữ liệu
import matplotlib.pyplot as plt
import seaborn as sns

# <PERSON>hư viện machine learning
import sklearn
from sklearn.experimental import enable_halving_search_cv
from sklearn.preprocessing import OneHotEncoder, StandardScaler, OrdinalEncoder, LabelEncoder, FunctionTransformer
from sklearn.model_selection import cross_validate, train_test_split, cross_val_score, cross_val_predict
from sklearn.model_selection import RepeatedKFold, learning_curve, GridSearchCV, HalvingGridSearchCV
from sklearn.metrics import root_mean_squared_error, r2_score, make_scorer
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.feature_selection import SelectFromModel
from sklearn.base import BaseEstimator, TransformerMixin


# Th<PERSON> viện thống kê
import statsmodels.api as sm
import statsmodels.formula.api as smf
from scipy import stats
from scipy.stats import probplot
import pymc as pm
import arviz as az

# Các mô hình hồi quy
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.svm import LinearSVR
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
from sklearn.neural_network import MLPRegressor

# Import thư viện thời gian
import time
from datetime import timedelta

# Cấu hình hiển thị
pd.set_option('display.max_columns', None)
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Tắt cảnh báo từ sklearn
import warnings
from sklearn.exceptions import ConvergenceWarning
warnings.filterwarnings("ignore", category=ConvergenceWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# Kiểm tra phiên bản các thư viện
print("NumPy version:", np.__version__)
print("Pandas version:", pd.__version__)
print("Scikit-learn version:", sklearn.__version__)
print("XGBoost version:", xgb.__version__)
print("Matplotlib version:", plt.matplotlib.__version__)
print("Seaborn version:", sns.__version__)

# Đọc dữ liệu từ file CSV
df = pd.read_csv("E:/20250504_themtuyen_tonghop.csv")

# Hiển thị 5 dòng đầu tiên
df.head()

df.columns

df = df[['Ky_QT_adjust', 'Tuoi', 'GIOI_TINH', 'So_Ngay_DTri_adjust',
       'MA_LYDO_VVIEN', 'MA_LOAI_KCB', 'regions', 'HI', 'insurance', 
       'tuyen', 'checkComorbidities', 'comorbidities', 'loaiBienChung', 
       'soBienChung', 'diemBienChung', "type", 'T_TONGCHI',]]

# Đổi tên cột
df.columns = ['Năm điều trị', 'Độ tuổi', 'Giới tính', 'Số ngày điều trị',
       'Lý do vào viện', 'Hình thức khám chữa bệnh', 'Vùng - khu vực đăng ký BHYT', 'Nhóm đối tượng tham gia BHYT', 'Mức hưởng BHYT',
       'Tuyến bệnh viện điều trị', 'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng',
       'Số biến chứng', 'Điểm biến chứng', 'Nơi điều trị', 'Tổng chi phí']

# Kiểm tra kích thước dữ liệu
print(f"Kích thước dữ liệu: {df.shape[0]} dòng x {df.shape[1]} cột")

# Phương pháp 1: Lấy mẫu ngẫu nhiên từ dữ liệu
# Lấy mẫu 10% dữ liệu để phân tích nhanh hơn
sample_size = 0.1  # 10% dữ liệu
df_sampled = df.sample(frac=sample_size, random_state=42)
print(f"Kích thước dữ liệu sau khi lấy mẫu: {df_sampled.shape[0]} dòng x {df_sampled.shape[1]} cột")
print(f"Tỷ lệ so với dữ liệu gốc: {df_sampled.shape[0] / df.shape[0] * 100:.2f}%")

# Sử dụng df_sampled thay vì df cho các phân tích ban đầu và thử nghiệm mô hình
# Sau khi tìm được mô hình tốt nhất, có thể huấn luyện lại trên toàn bộ dữ liệu

# Phương pháp 2: Sử dụng thư viện Dask cho dữ liệu lớn
# Uncomment đoạn code dưới đây nếu muốn sử dụng Dask
"""
import dask.dataframe as dd

# Đọc dữ liệu với Dask
dask_df = dd.read_csv('data_for_training.csv')
print(f"Kích thước dữ liệu Dask: {len(dask_df)} dòng x {len(dask_df.columns)} cột")

# Thực hiện các phép tính trên Dask DataFrame
# Ví dụ: tính giá trị trung bình của một cột
mean_value = dask_df['T_TONGCHI'].mean().compute()
print(f"Giá trị trung bình của chi phí: {mean_value}")
"""

# Lưu ý: Để sử dụng Dask, cần cài đặt thư viện: pip install dask[dataframe]

# Kiểm tra thông tin cơ bản về dữ liệu
df.info()

# Kiểm tra giá trị thiếu
missing_values = df.isnull().sum()
missing_percentage = (missing_values / len(df)) * 100

missing_data = pd.DataFrame({
    'Số giá trị thiếu': missing_values,
    'Phần trăm': missing_percentage
})

# Hiển thị các cột có giá trị thiếu
missing_data[missing_data['Số giá trị thiếu'] > 0].sort_values('Số giá trị thiếu', ascending=False)

# Tạo cột log của "Tổng chi phí"
df["log(Tổng chi phí)"] = np.log(df["Tổng chi phí"])

# Xoá cột "Tổng chi phí"
df = df.drop(columns=["Tổng chi phí"])

# Tạo cột "Nhóm tuổi"
df["Nhóm tuổi"] = np.where(df["Độ tuổi"] < 40, 1,
                     np.where(df["Độ tuổi"] < 60, 2, 3))

# Tạo cột "Số biến chứng"
df["Số biến chứng"] = df["Số biến chứng"].apply(
    lambda x: "≥3" if x >= 3 else str(x)
)

# Tạo cột "Điểm biến chứng"
df["Điểm biến chứng"] = df["Điểm biến chứng"].apply(
    lambda x: "≥3" if x >= 3 else str(x)
)

# Đọc chỉ số đã lưu
idx = pd.read_csv("idx_sai_so_lon.csv").squeeze()  # chuyển thành Series

# Lọc điều kiện tương đương:
df = df[(df["Số ngày điều trị"] < 30) & (~df.index.isin(idx))]

# Chuyển đổi kiểu dữ liệu
categorical_columns = ['Giới tính', 'Lý do vào viện', 'Hình thức khám chữa bệnh', 'Bệnh kèm', 'Số biến chứng', 'Điểm biến chứng']
df[categorical_columns] = df[categorical_columns].astype('object')

# Kiểm tra dữ liệu sau khi xử lý
df.head()

# Thống kê mô tả cho các biến số
df.describe()

# Thống kê mô tả cho các biến phân loại
df.describe(include=['object'])

# Phân tích phân phối của biến mục tiêu gốc (Tổng chi phí)
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
sns.histplot(df['Tổng chi phí'], kde=True)
plt.title('Phân phối Tổng chi phí')
plt.xlabel('Tổng chi phí (VND)')
plt.ylabel('Tần suất')

# Phân tích phân phối của biến mục tiêu sau khi biến đổi logarit
plt.subplot(1, 2, 2)
sns.histplot(df['log(Tổng chi phí)'], kde=True)
plt.title('Phân phối Tổng chi phí (đã biến đổi logarit)')
plt.xlabel('log(Tổng chi phí)')
plt.ylabel('Tần suất') 
plt.tight_layout()
plt.show()

# Giải thích lý do sử dụng biến đổi logarit
print("Nhận xét:")
print("- Phân phối Tổng chi phí ban đầu bị lệch phải mạnh (right-skewed)")
print("- Sau khi biến đổi logarit, phân phối gần với phân phối chuẩn hơn")
print("- Biến đổi logarit giúp cải thiện hiệu suất của các mô hình hồi quy")

# Kiểm tra tính chuẩn của biến mục tiêu sau khi biến đổi logarit bằng Q-Q plot
plt.figure(figsize=(10, 6))
probplot(df['log(Tổng chi phí)'], plot=plt)
plt.title('Q-Q Plot cho biến Tổng chi phí (đã biến đổi logarit)')
plt.show()

print("Nhận xét:")
print("- Q-Q plot cho thấy biến đổi logarit đã cải thiện tính chuẩn của dữ liệu")
print("- Vẫn có một số điểm lệch khỏi đường thẳng ở hai đầu, nhưng phần lớn dữ liệu đã phù hợp với phân phối chuẩn")

# Ma trận tương quan giữa các biến số
numeric_df = df.select_dtypes(include=['float64', 'int64']).drop(columns=['Tổng chi phí'])
plt.figure(figsize=(14, 12))
correlation_matrix = numeric_df.corr()
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', mask=mask)
plt.title('Ma trận tương quan giữa các biến số', fontsize=16)
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

# Hiển thị các tương quan mạnh nhất với biến mục tiêu
target_correlations = correlation_matrix['log(Tổng chi phí)'].sort_values(ascending=False)
print("Tương quan với biến mục tiêu (log(Tổng chi phí)):")
print(target_correlations)

# Phân tích mối quan hệ giữa biến mục tiêu và các biến số quan trọng
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# Mối quan hệ với tuổi
sns.scatterplot(x='Độ tuổi', y='log(Tổng chi phí)', data=df, alpha=0.5, ax=axes[0])
axes[0].set_title('Mối quan hệ giữa Tuổi và Tổng chi phí')
axes[0].set_xlabel('Độ tuổi')
axes[0].set_ylabel('Log(Tổng chi phí)')

# Mối quan hệ với số ngày điều trị
sns.scatterplot(x='Số ngày điều trị', y='log(Tổng chi phí)', data=df, alpha=0.5, ax=axes[1])
axes[1].set_title('Mối quan hệ giữa Số ngày điều trị và Tổng chi phí')
axes[1].set_xlabel('Số ngày điều trị')
axes[1].set_ylabel('Log(Tổng chi phí)')

plt.tight_layout()
plt.show()

# Phân tích chi phí theo giới tính
plt.figure(figsize=(12, 6))
sns.boxplot(x='Giới tính', y='log(Tổng chi phí)', data=df)
plt.title('Chi phí điều trị theo Giới tính', fontsize=14)
plt.xlabel('Giới tính (1: Nam, 2: Nữ)')
plt.ylabel('Log(Tổng chi phí)')
plt.show()

# Thống kê chi phí trung bình theo giới tính
gender_stats = df.groupby('Giới tính')['log(Tổng chi phí)'].agg(['mean', 'median', 'std', 'count'])
print("Thống kê chi phí theo giới tính:")
print(gender_stats)

# Phân tích chi phí theo khu vực
plt.figure(figsize=(14, 8))
sns.boxplot(x='Vùng - khu vực đăng ký BHYT', y='log(Tổng chi phí)', data=df)
plt.title('Chi phí điều trị theo Khu vực', fontsize=14)
plt.xlabel('Khu vực')
plt.ylabel('Log(Tổng chi phí)')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

# Thống kê chi phí trung bình theo khu vực
region_stats = df.groupby('Vùng - khu vực đăng ký BHYT')['log(Tổng chi phí)'].agg(['mean', 'median', 'std', 'count']).sort_values('mean', ascending=False)
print("Thống kê chi phí theo khu vực (sắp xếp theo chi phí trung bình):")
print(region_stats)

# Phân tích chi phí theo loại biến chứng
plt.figure(figsize=(14, 8))
sns.boxplot(x='Loại biến chứng', y='log(Tổng chi phí)', data=df)
plt.title('Chi phí điều trị theo Loại biến chứng', fontsize=14)
plt.xlabel('Loại biến chứng')
plt.ylabel('Log(Tổng chi phí)')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

# Thống kê chi phí trung bình theo loại biến chứng
complication_stats = df.groupby('Loại biến chứng')['log(Tổng chi phí)'].agg(['mean', 'median', 'std', 'count']).sort_values('mean', ascending=False)
print("Thống kê chi phí theo loại biến chứng (sắp xếp theo chi phí trung bình):")
print(complication_stats)

# Kiểm tra outliers trong biến mục tiêu
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
sns.boxplot(y=df['Tổng chi phí'])
plt.title('Boxplot Tổng chi phí')
plt.ylabel('Tổng chi phí (VND)')

plt.subplot(1, 2, 2)
sns.boxplot(y=df['log(Tổng chi phí)'])
plt.title('Boxplot Tổng chi phí (đã biến đổi logarit)')
plt.ylabel('Log(Tổng chi phí)')
plt.tight_layout()
plt.show()

# Xác định các outliers trong biến mục tiêu đã biến đổi logarit
Q1 = df['log(Tổng chi phí)'].quantile(0.25)
Q3 = df['log(Tổng chi phí)'].quantile(0.75)
IQR = Q3 - Q1
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

outliers = df[(df['log(Tổng chi phí)'] < lower_bound) | (df['log(Tổng chi phí)'] > upper_bound)]
print(f"Số lượng outliers trong biến mục tiêu: {len(outliers)}")
print(f"Tỷ lệ outliers: {len(outliers) / len(df) * 100:.2f}%")

# Kiểm tra lại dữ liệu thiếu
print("Số lượng giá trị thiếu trong mỗi cột:")
print(df.isnull().sum())

# Nếu có dữ liệu thiếu, chúng ta có thể xử lý ở đây
# Ví dụ: df = df.dropna() hoặc df['column'].fillna(value)

# Quyết định về cách xử lý outliers
# Trong trường hợp này, chúng ta sẽ giữ lại outliers vì chúng có thể chứa thông tin quan trọng
# và biến đổi logarit đã giúp giảm ảnh hưởng của chúng
print("\nNhận xét về outliers:")
print("- Giữ lại outliers vì chúng có thể đại diện cho các trường hợp đặc biệt")
print("- Biến đổi logarit đã giúp giảm ảnh hưởng của outliers")
print("- Các mô hình như Random Forest và XGBoost ít bị ảnh hưởng bởi outliers")

# Định nghĩa biến mục tiêu và các biến đầu vào
y = df['log(Tổng chi phí)']
X = df.drop(['log(Tổng chi phí)'], axis=1)

# Phân chia dữ liệu thành tập huấn luyện và tập kiểm tra
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print(f"Kích thước tập huấn luyện: {X_train.shape[0]} mẫu")
print(f"Kích thước tập kiểm tra: {X_test.shape[0]} mẫu")

# Phân loại các biến thành biến số và biến phân loại
categorical_features = ['Giới tính', 'Lý do vào viện', 'Hình thức khám chữa bệnh',
       'Vùng - khu vực đăng ký BHYT', 'Nhóm đối tượng tham gia BHYT',
       # 'Phân hạng CSKCB', 
       'Bệnh kèm', 'Loại bệnh kèm', 'Loại biến chứng', 'Nơi điều trị']

ordinal_features = ['Mức hưởng BHYT', 'Tuyến bệnh viện điều trị', 'Số biến chứng',
       'Điểm biến chứng', 'Nhóm tuổi']

numerical_features = ['Năm điều trị', 'Độ tuổi', 'Số ngày điều trị']

# Kiểm tra các biến phân loại
print("Số lượng giá trị duy nhất trong mỗi biến phân loại:")
for feature in categorical_features:
    print(f"{feature}: {X[feature].nunique()} giá trị duy nhất")

# Tạo pipeline tiền xử lý dữ liệu
# Xử lý biến số: chuẩn hóa
numerical_transformer = Pipeline(steps=[
    ('scaler', StandardScaler())
])

# Xử lý biến phân loại: one-hot encoding
categorical_transformer = Pipeline(steps=[
    ('onehot', OneHotEncoder(handle_unknown='ignore', sparse_output=False))
])

ordinal_transformer = Pipeline(steps=[
    ('ordinal', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))
])

# Kết hợp các bước tiền xử lý
preprocessor = ColumnTransformer(
    transformers=[
        ('num', numerical_transformer, numerical_features),
        ('cat', categorical_transformer, categorical_features),
        ('ord', ordinal_transformer, ordinal_features),
    ])

# Kiểm tra số lượng đặc trưng sau khi tiền xử lý
preprocessed_X_train = preprocessor.fit_transform(X_train)
print(f"Số lượng đặc trưng sau khi tiền xử lý: {preprocessed_X_train.shape[1]}")

feature_names = []
for name, transformer, cols in preprocessor.transformers_:
    if name == 'remainder':
        continue
    if hasattr(transformer, 'get_feature_names_out'):
        names = transformer.get_feature_names_out(cols)
    else:
        names = cols  # nếu không có .get_feature_names_out (ví dụ với StandardScaler)
    feature_names.extend(names)

print(feature_names)

# Sử dụng dữ liệu mẫu để huấn luyện mô hình
sample_size = 0.1  # 10% dữ liệu
df_sampled = pd.concat([X_train, y_train], axis=1).sample(frac=sample_size, random_state=42)
print(f"Kích thước dữ liệu sau khi lấy mẫu: {df_sampled.shape[0]} dòng x {df_sampled.shape[1]} cột")

# Sử dụng df_sampled thay vì df cho các bước tiếp theo
# Định nghĩa biến mục tiêu và các biến đầu vào từ dữ liệu đã lấy mẫu
y = df_sampled['log(Tổng chi phí)']
X = df_sampled.drop(['log(Tổng chi phí)'], axis=1)

# Định nghĩa các mô hình cơ bản (không tối ưu)
models_basic = {
    'Linear Regression': LinearRegression(),
    'Ridge': Ridge(),
    'Lasso': Lasso(),
    'ElasticNet': ElasticNet(),
    'Random Forest': RandomForestRegressor(random_state=42),
    'XGBoost': xgb.XGBRegressor(random_state=42),
    'SVR': LinearSVR(random_state=42),
    'MLP': MLPRegressor(random_state=42, max_iter=1000)
}

models = models_basic

# Định nghĩa các mô hình tối ưu cho chi phí tính toán
models_optimized = {
    # Các mô hình tuyến tính
    'Linear Regression': LinearRegression(),
    'Ridge': Ridge(alpha=1.0),
    'Lasso': Lasso(alpha=0.001),
    'ElasticNet': ElasticNet(alpha=0.001),
    
    # Random Forest
    'Random Forest': RandomForestRegressor(
        n_estimators=50,       # Giảm số lượng cây (mặc định là 100)
        max_depth=10,          # Giới hạn độ sâu tối đa của cây
        min_samples_split=10,  # Tăng số lượng mẫu tối thiểu để phân tách
        min_samples_leaf=4,    # Tăng số lượng mẫu tối thiểu ở nút lá
        max_features='sqrt',   # Chỉ xem xét một tập con các đặc trưng
        n_jobs=-1,             # Sử dụng tất cả các lõi CPU
        random_state=42
    ),
    
    # XGBoost
    'XGBoost': xgb.XGBRegressor(
        # n_estimators=50,       # Giảm số lượng cây
        # max_depth=6,           # Giới hạn độ sâu
        # learning_rate=0.1,     # Tốc độ học
        # subsample=0.8,         # Lấy mẫu con từ dữ liệu
        # colsample_bytree=0.8,  # Lấy mẫu con từ các đặc trưng
        # tree_method='hist',    # Phương pháp xây dựng cây nhanh hơn
        n_jobs=-1,             # Sử dụng tất cả các lõi CPU
        random_state=42
    ),
    
    # SVR
    'SVR': LinearSVR(
        C=1.0,                # Tham số điều chỉnh
        epsilon=0.1,          # Biên độ ống
        dual=True,           # Sử dụng công thức primal (nhanh hơn với dữ liệu lớn)
        max_iter=1000,        # Giới hạn số vòng lặp
        random_state=42
    ),
    
    # MLP
    'MLP': MLPRegressor(
        hidden_layer_sizes=(50,),  # Giảm kích thước mạng
        max_iter=300,              # Giảm số vòng lặp tối đa
        early_stopping=True,       # Dừng sớm nếu không cải thiện
        validation_fraction=0.1,   # Phần dữ liệu dùng để xác thực
        n_iter_no_change=10,       # Số vòng lặp không cải thiện trước khi dừng
        random_state=42
    )
}

# Sử dụng mô hình tối ưu
models = models_optimized

# Hàm tính metrics
def r2_dev_exp(y_true_log, y_pred_log):
    y_true = np.exp(y_true_log)
    y_pred = np.exp(y_pred_log)
    eps = 1e-9
    dev_model = 2 * np.sum((y_true - y_pred) / y_pred - np.log(y_true / y_pred))
    y_mean = np.mean(y_true)
    dev_null = 2 * np.sum((y_true - y_mean) / y_mean - np.log(y_true / y_mean))
    return 1 - dev_model / dev_null

def rmse_exp(y_true_log, y_pred_log):
    return np.sqrt(mean_squared_error(np.exp(y_true_log), np.exp(y_pred_log)))

def mae_exp(y_true_log, y_pred_log):
    return mean_absolute_error(np.exp(y_true_log), np.exp(y_pred_log))

# Scoring dictionary
scoring = {
    'r2_deviance': make_scorer(r2_dev_exp, greater_is_better=True),
    'rmse_exp': make_scorer(rmse_exp, greater_is_better=False),
    'mae_exp': make_scorer(mae_exp, greater_is_better=False),
}

# Hàm đánh giá mô hình bằng cross-validation có tính thời gian
def evaluate_model(model, X, y_log, preprocessor, cv=5):
    pipeline = Pipeline(steps=[
        ('preprocessor', preprocessor),
        ('model', model)
    ])

    start_time = time.time()
    cv_results = cross_validate(pipeline, X, y_log, cv=cv, scoring=scoring, return_train_score=True)
    training_time = time.time() - start_time

    results = {
        'R² (test)': cv_results['test_r2_deviance'].mean(),
        'RMSE (test)': -cv_results['test_rmse_exp'].mean(),
        'MAE (test)': -cv_results['test_mae_exp'].mean(),
        'R² (train)': cv_results['train_r2_deviance'].mean(),
        'RMSE (train)': -cv_results['train_rmse_exp'].mean(),
        'MAE (train)': -cv_results['train_mae_exp'].mean(),
        'Thời gian huấn luyện': str(timedelta(seconds=training_time))
    }

    return results

# Đánh giá tất cả các mô hình
results = {}
for name, model in models.items():
    print(f"Đang đánh giá mô hình: {name}...")
    results[name] = evaluate_model(model, X, y, preprocessor)
    print(f"Mô hình {name} đã hoàn thành trong: {results[name]['Thời gian huấn luyện']}")
    print(f"R² (test): {results[name]['R² (test)']:.4f}, RMSE (test): {results[name]['RMSE (test)']:.4f}")
    print("-" * 50)

# Tạo DataFrame kết quả
results_df = pd.DataFrame(results).T
results_df = results_df.sort_values('R² (test)', ascending=False)
results_df

# Đánh giá tất cả các mô hình
results = {}
for name, model in models.items():
    print(f"Đang đánh giá mô hình: {name}...")
    results[name] = evaluate_model(model, X, y, preprocessor)
    print(f"Mô hình {name} đã hoàn thành trong: {results[name]['Thời gian huấn luyện']}")
    print(f"R² (test): {results[name]['R² (test)']:.4f}, RMSE (test): {results[name]['RMSE (test)']:.4f}")
    print("-" * 50)

# Tạo DataFrame kết quả
results_df = pd.DataFrame(results).T
results_df = results_df.sort_values('R² (test)', ascending=False)
results_df

import pandas as pd

data = {
    'Model': ['XGBoost', 'MLP', 'RF', 'SVR', 'Ridge', 'LR', 'ElasticNet', 'Lasso'],
    'R² (test)': [0.588335, 0.581178, 0.539742, 0.500237, 0.47288, 0.472857, 0.471039, 0.470233],
    'RMSE (test)': [2664375.027684, 2670683.642763, 2840879.673661, 2809408.718038, 2796593.345479,
                    2796334.925905, 2800872.884196, 2801723.119593],
    'MAE (test)': [397828.948515, 400926.218968, 433374.729469, 453576.531012, 458043.375217,
                   458033.73658, 458366.887644, 458551.040692],
    'R² (train)': [0.594777, 0.582383, 0.542226, 0.500274, 0.473, 0.472974, 0.471144, 0.470327],
    'RMSE (train)': [2588495.287886, 2670239.698124, 2830578.569309, 2811126.07197, 2797251.615043,
                     2797177.853762, 2801580.02141, 2802409.287104],
    'MAE (train)': [390051.051825, 400135.711601, 431736.434099, 453600.964517, 458005.50444,
                    457997.666456, 458335.20059, 458523.387182],
    'Thời gian huấn luyện': ['0:00:31.620794', '0:07:12.471982', '0:00:50.794112', '0:16:56.618118',
                             '0:00:23.333807', '0:00:27.886322', '0:01:00.072316', '0:00:33.785983']
}

results_df = pd.DataFrame(data)
# results_df.to_csv('res_df.csv')


import matplotlib.pyplot as plt

plt.figure(figsize=(14, 10))

# Biểu đồ R²
plt.subplot(1, 1, 1)
ax1 = plt.gca()
bars1 = ax1.bar(results_df['Model'], results_df['R² (test)'], color='skyblue')
plt.title('So sánh R² giữa các mô hình', fontsize=14)
plt.ylabel('R²')
plt.ylim(0, 1)
# plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Thêm nhãn cho từng cột
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2, height + 0.01, f'{height:.2f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.show()


import matplotlib.pyplot as plt

plt.figure(figsize=(14, 10))

# Biểu đồ R²
plt.subplot(2, 1, 1)
ax1 = plt.gca()
bars1 = ax1.bar(results_df['Model'], results_df['RMSE (test)'], color='burlywood')
plt.title('So sánh RMSE giữa các mô hình', fontsize=14)
plt.ylabel('RMSE')
plt.ylim(0, 3e6)
# plt.xticks(rotation=45, ha='right')
# plt.grid(axis='y', linestyle='--', alpha=0.7)

# Thêm nhãn cho từng cột
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2, height + 0.01, f'{height:,.0f}', ha='center', va='bottom', fontsize=9)

# Biểu đồ RMSE
plt.subplot(2, 1, 2)
ax2 = plt.gca()
bars2 = ax2.bar(results_df['Model'], results_df['MAE (test)'], color='salmon')
plt.title('So sánh MAE giữa các mô hình', fontsize=14)
plt.ylabel('MAE')
plt.ylim(0, 5e5)
# plt.xticks(rotation=45, ha='right')
# plt.grid(axis='y', linestyle='--', alpha=0.7)

# Thêm nhãn cho từng cột
for bar in bars2:
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2, height + 10000, f'{height:,.0f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.show()


# Trực quan hóa thời gian huấn luyện
# Chuyển đổi chuỗi thời gian thành số giây
import re

def time_to_seconds(time_str):
    # Mẫu: '0:00:12.345678'
    pattern = r'(\d+):(\d+):(\d+\.\d+)'
    match = re.match(pattern, time_str)
    if match:
        hours, minutes, seconds = match.groups()
        return int(hours) * 3600 + int(minutes) * 60 + float(seconds)
    return 0

# Tạo cột thời gian huấn luyện dưới dạng số giây
results_df['Thời gian (giây)'] = results_df['Thời gian huấn luyện'].apply(time_to_seconds)

# Vẽ biểu đồ thời gian huấn luyện
plt.figure(figsize=(12, 6))
results_df['Thời gian (giây)'].plot(kind='bar', color='green')
plt.title('Thời gian huấn luyện của các mô hình', fontsize=14)
plt.ylabel('Thời gian (giây)')
plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', linestyle='--', alpha=0.7)

# Thêm nhãn thời gian lên mỗi cột
for i, v in enumerate(results_df['Thời gian (giây)']):
    plt.text(i, v + 0.1, f"{v:.1f}s", ha='center')

plt.tight_layout()
plt.show()

# # Lưu ý: Đoạn code này chỉ chạy nếu muốn so sánh hiệu suất giữa mô hình cơ bản và mô hình tối ưu
# def compare_models(run_comparison=False):
#     if not run_comparison:
#         print("Bỏ qua so sánh mô hình. Đặt run_comparison=True nếu muốn so sánh.")
#         return
    
#     # Đánh giá mô hình cơ bản
#     print("Đánh giá các mô hình cơ bản...")
#     results_basic = {}
#     for name, model in models_basic.items():
#         print(f"Đang đánh giá mô hình cơ bản: {name}...")
#         results_basic[name] = evaluate_model(model, X_train, y_train, preprocessor)
#         print(f"Mô hình {name} đã hoàn thành trong: {results_basic[name]['Thời gian huấn luyện']}")
#         print("-" * 50)
    
#     # Đánh giá mô hình tối ưu
#     print("\nĐánh giá các mô hình tối ưu...")
#     results_optimized = {}
#     for name, model in models_optimized.items():
#         print(f"Đang đánh giá mô hình tối ưu: {name}...")
#         results_optimized[name] = evaluate_model(model, X_train, y_train, preprocessor)
#         print(f"Mô hình {name} đã hoàn thành trong: {results_optimized[name]['Thời gian huấn luyện']}")
#         print("-" * 50)
    
#     # Tạo DataFrame kết quả
#     results_df_basic = pd.DataFrame(results_basic).T
#     results_df_basic['Loại'] = 'Cơ bản'
#     results_df_optimized = pd.DataFrame(results_optimized).T
#     results_df_optimized['Loại'] = 'Tối ưu'
    
#     # Gộp kết quả
#     comparison_df = pd.concat([results_df_basic, results_df_optimized])
    
#     # Chuyển đổi thời gian huấn luyện thành số giây
#     comparison_df['Thời gian (giây)'] = comparison_df['Thời gian huấn luyện'].apply(time_to_seconds)
    
#     # Tạo DataFrame so sánh
#     models_to_compare = ['Random Forest', 'XGBoost', 'SVR', 'MLP']
#     comparison_data = []
    
#     for model_name in models_to_compare:
#         basic_time = comparison_df[(comparison_df.index == model_name) & (comparison_df['Loại'] == 'Cơ bản')]['Thời gian (giây)'].values[0]
#         optimized_time = comparison_df[(comparison_df.index == model_name) & (comparison_df['Loại'] == 'Tối ưu')]['Thời gian (giây)'].values[0]
#         basic_r2 = comparison_df[(comparison_df.index == model_name) & (comparison_df['Loại'] == 'Cơ bản')]['R² (test)'].values[0]
#         optimized_r2 = comparison_df[(comparison_df.index == model_name) & (comparison_df['Loại'] == 'Tối ưu')]['R² (test)'].values[0]
        
#         speedup = basic_time / optimized_time if optimized_time > 0 else float('inf')
#         r2_change = optimized_r2 - basic_r2
        
#         comparison_data.append({
#             'Mô hình': model_name,
#             'Thời gian cơ bản (giây)': basic_time,
#             'Thời gian tối ưu (giây)': optimized_time,
#             'Tăng tốc (lần)': speedup,
#             'R² cơ bản': basic_r2,
#             'R² tối ưu': optimized_r2,
#             'Thay đổi R²': r2_change
#         })
    
#     comparison_result = pd.DataFrame(comparison_data)
#     display(comparison_result)
    
#     # Vẽ biểu đồ so sánh thời gian
#     plt.figure(figsize=(14, 6))
    
#     # Dữ liệu cho biểu đồ
#     models = comparison_result['Mô hình']
#     basic_times = comparison_result['Thời gian cơ bản (giây)']
#     optimized_times = comparison_result['Thời gian tối ưu (giây)']
    
#     # Vị trí các cột
#     x = np.arange(len(models))
#     width = 0.35
    
#     # Vẽ biểu đồ
#     plt.bar(x - width/2, basic_times, width, label='Cơ bản', color='skyblue')
#     plt.bar(x + width/2, optimized_times, width, label='Tối ưu', color='salmon')
    
#     # Thêm nhãn và tiêu đề
#     plt.xlabel('Mô hình')
#     plt.ylabel('Thời gian (giây)')
#     plt.title('So sánh thời gian huấn luyện giữa mô hình cơ bản và tối ưu')
#     plt.xticks(x, models)
#     plt.legend()
    
#     # Thêm nhãn thời gian lên mỗi cột
#     for i, (v1, v2) in enumerate(zip(basic_times, optimized_times)):
#         plt.text(i - width/2, v1 + 1, f"{v1:.1f}s", ha='center')
#         plt.text(i + width/2, v2 + 1, f"{v2:.1f}s", ha='center')
    
#     plt.tight_layout()
#     plt.show()
    
#     return comparison_result

# # Chạy hàm với run_comparison=False để bỏ qua so sánh
# # Đặt run_comparison=True nếu muốn so sánh các mô hình
# comparison_result = compare_models(run_comparison=False)

# Chọn mô hình tốt nhất dựa trên kết quả cross-validation
best_model_name = results_df.index[0]
best_model = models_optimized[best_model_name]

print(f"Mô hình tốt nhất: {best_model_name}")
print(f"R² (cross-validation): {results_df.loc[best_model_name, 'R² (test)']:.4f}")
print(f"RMSE (cross-validation): {results_df.loc[best_model_name, 'RMSE (test)']:.4f}")

# Định nghĩa không gian tìm kiếm siêu tham số dựa trên loại mô hình
param_grid = {}

if best_model_name == 'Random Forest':
    param_grid = {
        'model__n_estimators': [50, 100, 200],
        'model__max_depth': [10, 15, 20, None],
        'model__min_samples_split': [2, 5, 10],
        'model__min_samples_leaf': [1, 2, 4]
    }
elif best_model_name == 'XGBoost':
    param_grid = {
        'model__n_estimators': [50, 100, 200],
        'model__max_depth': [3, 6, 9],
        'model__learning_rate': [0.01, 0.1, 0.2],
        'model__subsample': [0.8, 1.0],
        'model__colsample_bytree': [0.8, 1.0]
    }
elif best_model_name in ['Ridge', 'Lasso', 'ElasticNet']:
    param_grid = {
        'model__alpha': [0.0001, 0.001, 0.01, 0.1, 1.0, 10.0]
    }
    if best_model_name == 'ElasticNet':
        param_grid['model__l1_ratio'] = [0.1, 0.5, 0.7, 0.9]
elif best_model_name == 'SVR':
    param_grid = {
        'model__C': [0.1, 1.0, 10.0],
        'model__epsilon': [0.01, 0.1, 0.2]
    }
elif best_model_name == 'MLP':
    param_grid = {
        'model__hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
        'model__activation': ['relu', 'tanh'],
        'model__alpha': [0.0001, 0.001, 0.01],
        'model__learning_rate': ['constant', 'adaptive']
    }

# Tạo pipeline cho GridSearchCV
pipeline = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('model', best_model)
])

param_grid_1 = {
    'model__max_depth': [6, 8, 10],
    'model__min_child_weight': [1, 3, 5]
}

pipeline = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('model', xgb.XGBRegressor(objective='reg:gamma', learning_rate=0.1, n_estimators=100, random_state=42),)
])

search_1 = HalvingGridSearchCV(
    pipeline,
    param_grid=param_grid_1,
    cv=3,
    scoring='neg_mean_squared_error',
    verbose=1,
    n_jobs=-1
)

search_1.fit(X, y)
best_params_1 = search_1.best_params_

best_params_1

param_grid_2 = {
    'model__gamma': [0, 0.1],
    'model__reg_alpha': [0.05, 0.1, 0.5],
    'model__reg_lambda': [1, 2, 3]
}

pipeline = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('model', xgb.XGBRegressor(objective='reg:gamma', learning_rate=0.1, n_estimators=100, random_state=42, **best_params_1))
])

search_2 = HalvingGridSearchCV(
    pipeline,
    param_grid=param_grid_2,
    cv=3,
    scoring='neg_mean_squared_error',
    verbose=1,
    n_jobs=-1
)

search_2.fit(X, y)
best_params_2 = {**best_params_1, **search_2.best_params_}

best_params_2

param_grid_3 = {
    'model__subsample': [1.0],
    'model__colsample_bytree': [0.4, 0.5, 0.6]
}

pipeline = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('model', xgb.XGBRegressor(objective='reg:squarederror', learning_rate=0.1, n_estimators=100, random_state=42, **best_params_2))
])

search_3 = HalvingGridSearchCV(
    pipeline,
    param_grid=param_grid_3,
    cv=3,
    scoring='neg_mean_squared_error',
    verbose=1,
    n_jobs=-1
)

search_3.fit(X, y)
best_params_3 = {**best_params_2, **search_3.best_params_}


best_params_3

best_params_3 = {'model__max_depth': 8,
 'model__min_child_weight': 5,
 'model__gamma': 0.1,
 'model__reg_alpha': 1,
 'model__reg_lambda': 3,
 'model__colsample_bytree': 0.5,
 'model__subsample': 1.0}

X_test_processed = preprocessor.transform(X_test)

# Cập nhật pipeline với learning_rate mới và n_estimators lớn
pipeline = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('model', xgb.XGBRegressor(objective='reg:gamma', learning_rate=0.05, n_estimators=1000, random_state=42, **best_params_3,
                               eval_metric='mse', early_stopping_rounds=20))
])

# Huấn luyện với early stopping
pipeline.fit(
    X_train, y_train,
    model__eval_set=[(X_test_processed, y_test)],
    model__verbose=False
)

y_pred = pipeline.predict(X_test)

import joblib

# Lưu pipeline đã huấn luyện
joblib.dump(pipeline, 'trained_pipeline_xgb.pkl')

import joblib
pipeline = joblib.load('trained_pipeline_xgb.pkl')

pipeline.named_steps['model'].get_params()['n_estimators']

y_pred_train = pipeline.predict(X_train)
y_pred_test = pipeline.predict(X_test)

print(r2_dev_exp(y_train, y_pred_train))
print(rmse_exp(y_train, y_pred_train))
print(mae_exp(y_train, y_pred_train))

print(r2_dev_exp(y_test, y_pred_test))
print(rmse_exp(y_test, y_pred_test))
print(mae_exp(y_test, y_pred_test))

rmse_exp(y_test, y_pred)

# Bắt đầu tính thời gian
start_time = time.time()

# Thực hiện GridSearchCV
print("Bắt đầu tìm kiếm siêu tham số tối ưu...")
grid_search = GridSearchCV(
    pipeline,
    param_grid=param_grid,
    cv=5,
    scoring='r2',
    # n_jobs=-1,
    verbose=1
)
grid_search.fit(X, y)

# Kết thúc tính thời gian
end_time = time.time()
training_time = end_time - start_time
print(f"Thời gian tìm kiếm siêu tham số: {str(timedelta(seconds=training_time))}")

# Hiển thị kết quả tốt nhất
print("\nSiêu tham số tối ưu:")
for param, value in grid_search.best_params_.items():
    print(f"{param}: {value}")

print(f"\nĐiểm R² tốt nhất: {grid_search.best_score_:.4f}")

# Lấy mô hình tốt nhất
best_pipeline = grid_search.best_estimator_

# Dự đoán trên tập kiểm tra
y_pred = best_pipeline.predict(X_test)

# Đánh giá hiệu suất trên tập kiểm tra
r2 = r2_score(y_test, y_pred)
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
mae = mean_absolute_error(y_test, y_pred)

print("\nHiệu suất trên tập kiểm tra với siêu tham số tối ưu:")
print(f"R²: {r2:.4f}")
print(f"RMSE: {rmse:.4f}")
print(f"MAE: {mae:.4f}")

# Bắt đầu tính thời gian
start_time = time.time()

best_pipeline.fit(X_train, y_train)

# Kết thúc tính thời gian
end_time = time.time()
training_time = end_time - start_time
print(f"Thời gian huấn luyện mô hình tốt nhất: {str(timedelta(seconds=training_time))}")

# Dự đoán trên tập kiểm tra
y_pred = best_pipeline.predict(X_test)

# Đánh giá hiệu suất trên tập kiểm tra
r2 = r2_score(y_test, y_pred)
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
mae = mean_absolute_error(y_test, y_pred)

print("\nHiệu suất trên tập kiểm tra:")
print(f"R²: {r2:.4f}")
print(f"RMSE: {rmse:.4f}")
print(f"MAE: {mae:.4f}")

# Bắt đầu tính thời gian
start_time = time.time()

best_pipeline.fit(X_train, y_train)

# Kết thúc tính thời gian
end_time = time.time()
training_time = end_time - start_time
print(f"Thời gian huấn luyện mô hình tốt nhất: {str(timedelta(seconds=training_time))}")

# Dự đoán trên tập kiểm tra
y_pred = best_pipeline.predict(X_test)

# Đánh giá hiệu suất trên tập kiểm tra
r2 = r2_score(y_test, y_pred)
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
mae = mean_absolute_error(y_test, y_pred)

print("\nHiệu suất trên tập kiểm tra:")
print(f"R²: {r2:.4f}")
print(f"RMSE: {rmse:.4f}")
print(f"MAE: {mae:.4f}")

# # Huấn luyện mô hình tốt nhất trên toàn bộ tập huấn luyện
# best_pipeline = Pipeline(steps=[
#     ('preprocessor', preprocessor),
#     ('model', XGBRegressor.XGBRegressor(colsample_bytree=1.0, learning_rate=0.1, max_depth=9, n_estimators=200, subsample=0.8, random_state=42))
# ])

best_pipeline.named_steps['model'].feature_importances_

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# Lấy tầm quan trọng
importances = best_pipeline.named_steps['model'].feature_importances_

# Tên biến phân loại
cat_feature_names = best_pipeline.named_steps['preprocessor'] \
                              .named_transformers_['cat'] \
                              .get_feature_names_out(categorical_features)

# Tổng hợp tên biến
feature_names = list(numerical_features) + list(cat_feature_names)

# DataFrame độ quan trọng
feature_importance_df = pd.DataFrame({
    'Feature': feature_names,
    'Importance': importances
})

# Top 10 biến quan trọng nhất
top_10_features = feature_importance_df.sort_values(by='Importance', ascending=False).head(10)

# Vẽ biểu đồ
plt.figure(figsize=(10, 6))
ax = sns.barplot(x='Importance', y='Feature', data=top_10_features, palette='viridis')

# Thêm nhãn lên thanh bar
for p in ax.patches:
    width = p.get_width()
    ax.text(width + 0.005,              # x: vị trí ngay sau chiều rộng của bar
            p.get_y() + p.get_height()/2,  # y: giữa chiều cao của bar
            f'{width:.4f}',              # nội dung
            va='center')

plt.title('Top 10 đặc trưng có ảnh hưởng lớn nhất trong mô hình')
plt.xlabel('Mức độ quan trọng của đặc trưng')
plt.ylabel('Feature')
plt.tight_layout()
plt.show()


# Định nghĩa biến mục tiêu và các biến đầu vào
y_corr = df_sampled['Tổng chi phí']
X_corr = df_sampled.drop(['log(Tổng chi phí)', 'Tổng chi phí'], axis=1)

# Phân phối của biến mục tiêu
plt.figure(figsize=(10, 6))
sns.histplot(y_corr, kde=True)
plt.title('Phân phối của biến mục tiêu')
plt.show()

# Kiểm định Shapiro-Wilk để xác nhận biến mục tiêu không có phân phối chuẩn
from scipy import stats
shapiro_test = stats.shapiro(y_corr)
print(f"Kiểm định Shapiro-Wilk cho biến mục tiêu: statistic={shapiro_test[0]:.4f}, p-value={shapiro_test[1]:.4f}")
if shapiro_test[1] < 0.05:
    print("Biến mục tiêu không có phân phối chuẩn (p < 0.05)")
else:
    print("Biến mục tiêu có phân phối chuẩn (p >= 0.05)")

# Tính hệ số tương quan Spearman cho các biến số
numeric_features = X_corr.select_dtypes(include=['int64', 'float64']).columns
spearman_corr = pd.DataFrame(columns=['Feature', 'Spearman_Correlation', 'P_Value'])

for feature in numeric_features:
    correlation, p_value = stats.spearmanr(X_corr[feature], y_corr, nan_policy='omit')
    new_row = {'Feature': feature, 'Spearman_Correlation': correlation, 'P_Value': p_value}
    spearman_corr = pd.concat([spearman_corr, pd.DataFrame([new_row])], ignore_index=True)

# Sắp xếp theo giá trị tuyệt đối của hệ số tương quan
spearman_corr = spearman_corr.sort_values(by='Spearman_Correlation', key=lambda x: abs(x), ascending=False)
spearman_corr['Significant'] = spearman_corr['P_Value'] < 0.05

print("Hệ số tương quan Spearman giữa các biến số và biến mục tiêu:")
print(spearman_corr)

# Vẽ biểu đồ heatmap cho ma trận tương quan Spearman
plt.figure(figsize=(12, 10))
corr_matrix = X_corr[numeric_features].join(y_corr).corr(method='spearman')
mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
sns.heatmap(corr_matrix, mask=mask, cmap='coolwarm', vmax=1, vmin=-1, center=0,
            square=True, linewidths=.5, annot=True, fmt=".2f")
plt.title('Ma trận tương quan Spearman', fontsize=16)
plt.tight_layout()
plt.show()

# Xác định các biến phân loại
categorical_features = X_corr.select_dtypes(include=['object', 'category']).columns.tolist()
if len(categorical_features) == 0:
    # Nếu không có biến phân loại, thử tìm các biến số có ít giá trị duy nhất
    for col in numeric_features:
        if X_corr[col].nunique() < 10:  # Ngưỡng để xem xét biến là phân loại
            categorical_features.append(col)

# Phân tích biến phân loại sử dụng kiểm định phi tham số
categorical_analysis = pd.DataFrame(columns=['Feature', 'Test', 'Statistic', 'P_Value', 'Significant'])

for feature in categorical_features:
    categories = X_corr[feature].unique()
    n_categories = len(categories)
    
    if n_categories == 2:  # Sử dụng Mann-Whitney U test cho 2 nhóm
        group1 = y_corr[X_corr[feature] == categories[0]]
        group2 = y_corr[X_corr[feature] == categories[1]]
        
        try:
            stat, p_value = stats.mannwhitneyu(group1, group2)
            test_name = 'Mann-Whitney U'
            
            new_row = {
                'Feature': feature,
                'Test': test_name,
                'Statistic': stat,
                'P_Value': p_value,
                'Significant': p_value < 0.05
            }
            categorical_analysis = pd.concat([categorical_analysis, pd.DataFrame([new_row])], ignore_index=True)
        except Exception as e:
            print(f"Không thể thực hiện kiểm định Mann-Whitney U cho biến {feature}: {e}")
    
    elif n_categories > 2:  # Sử dụng Kruskal-Wallis H test cho nhiều nhóm
        groups = [y_corr[X_corr[feature] == cat] for cat in categories]
        
        try:
            stat, p_value = stats.kruskal(*groups)
            test_name = 'Kruskal-Wallis H'
            
            new_row = {
                'Feature': feature,
                'Test': test_name,
                'Statistic': stat,
                'P_Value': p_value,
                'Significant': p_value < 0.05
            }
            categorical_analysis = pd.concat([categorical_analysis, pd.DataFrame([new_row])], ignore_index=True)
        except Exception as e:
            print(f"Không thể thực hiện kiểm định Kruskal-Wallis H cho biến {feature}: {e}")

# Sắp xếp theo p-value
categorical_analysis = categorical_analysis.sort_values('P_Value')

print("Kết quả phân tích biến phân loại:")
print(categorical_analysis)

# Vẽ boxplot cho các biến phân loại có ý nghĩa thống kê
significant_cat_features = categorical_analysis[categorical_analysis['Significant'] == True]['Feature'].tolist()

if len(significant_cat_features) > 0:
    for feature in significant_cat_features[:min(6, len(significant_cat_features))]:  # Giới hạn số lượng biểu đồ
        plt.figure(figsize=(10, 6))
        sns.boxplot(x=feature, y=y_corr.name, data=pd.concat([X, y_corr], axis=1))
        plt.title(f'Boxplot của {y_corr.name} theo {feature}')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()